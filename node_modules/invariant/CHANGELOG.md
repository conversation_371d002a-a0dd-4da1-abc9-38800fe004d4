2.2.4 / 2018-03-13
==================

  * Use flow strict mode (i.e. `@flow strict`).

2.2.3 / 2018-02-19
==================

  * Change license from BSD+Patents to MIT.

2.2.2 / 2016-11-15
==================

  * Add LICENSE file.
  * Misc housekeeping.

2.2.1 / 2016-03-09
==================

  * Use `NODE_ENV` variable instead of `__DEV__` to cache `process.env.NODE_ENV`.

2.2.0 / 2015-11-17
==================

  * Use `error.name` instead of `Invariant Violation`.

2.1.3 / 2015-11-17
==================

  * Remove `@provideModule` pragma.

2.1.2 / 2015-10-27
==================

  * Fix license.

2.1.1 / 2015-09-20
==================

  * Use correct SPDX license.
  * Test "browser.js" using browserify.
  * Switch from "envify" to "loose-envify".

2.1.0 / 2015-06-03
==================

  * Add "envify" as a dependency.
  * Fixed license field in "package.json".

2.0.0 / 2015-02-21
==================

  * Switch to using the "browser" field. There are now browser and server versions that respect the "format" in production.

1.0.2 / 2014-09-24
==================

  * Added tests, npmignore and gitignore.
  * Clarifications in README.

1.0.1 / 2014-09-24
==================

  * Actually include 'invariant.js'.

1.0.0 / 2014-09-24
==================

  * Initial release.
