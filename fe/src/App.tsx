import { useState, useEffect } from "react";
import { Header } from "./components/Header";
import { SearchSection, SearchFilters } from "./components/SearchSection";
import { UpcomingRides } from "./components/UpcomingRides";
import { PastRides } from "./components/PastRides";
import { getAllRides } from "./lib/api";
import ReviewModal from "./components/ReviewModal";
import { ThemeProvider } from "./components/ThemeProvider";
import { AuthModal } from "./components/AuthModal";
import { RegisterModal } from "./components/RegisterModal";
import { Footer } from "./components/Footer";
import { getStoredUser, removeUser, clearAllAuthState, setupTokenExpirationCheck } from "./lib/auth";
import type { User } from "./types";
import { SubscriptionBanner } from './components/SubscriptionBanner';
import { toast } from 'react-toastify';
import { getPendingPassengerReviews, getPendingDriverReviews } from "./lib/api";
import { Bell } from "lucide-react";
import { StripeStatusContext } from "./context/StripeStatusContext";
import { FeedbackForm } from "./components/FeedbackForm";

import { Elements } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';

const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY);

type AppProps = {
  region: string;
};

function App({ region }: AppProps) {
  const [searchFilters, setSearchFilters] = useState<SearchFilters>({
    from: "",
    to: "",
    date: "",
    colleges: []
  });

  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [isRegisterModalOpen, setIsRegisterModalOpen] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [pendingReviews, setPendingReviews] = useState<{ passenger: any[]; driver: any[] }>({
    passenger: [],
    driver: [],
  });
  const [isReviewModalOpen, setIsReviewModalOpen] = useState(false);
  const [stripeStatus, setStripeStatus] = useState<{ payouts_enabled: boolean } | undefined>(undefined);

  const [rides, setRides] = useState<any[]>([]);

  // Use region-specific storage key
  const storageKey = `revy_user_${region}`;

  // Add useEffect to fetch rides
  useEffect(() => {
    async function fetchRides() {
      try {
        const fetchedRides = await getAllRides();
        setRides(fetchedRides);
      } catch (error) {
        console.error("Error fetching rides:", error);
        setRides([]);
      }
    }
    fetchRides();
  }, [region]); // Keep region as dependency if you want to refetch when region changes

  useEffect(() => {
    async function fetchPendingReviews() {
      if (user) {
        try {
          const passengerReviews = await getPendingPassengerReviews(user.id);
          const driverReviews = await getPendingDriverReviews(user.id);
          setPendingReviews({
            passenger: passengerReviews,
            driver: driverReviews,
          });
          console.log({ "Pending reviews": pendingReviews });
        } catch (error) {
          console.error("Error fetching pending reviews:", error);
          setPendingReviews({ passenger: [], driver: [] });
        }
      } else {
        setPendingReviews({ passenger: [], driver: [] });
      }
    }
    fetchPendingReviews();
  }, [user]);

  useEffect(() => {
    const storedUser = getStoredUser();
    if (storedUser) {
      setUser(storedUser);
    }

    // Listen for token expiration events
    const handleTokenExpired = (event: CustomEvent) => {
      // Only handle events for the current region
      if (event.detail.region === region) {
        setUser(null);
        // Show a notification to the user
        toast.info('Your session has expired. Please sign in again.', {
          position: "top-center",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
        });
      }
    };

    window.addEventListener('auth:expired', handleTokenExpired as EventListener);

    return () => {
      window.removeEventListener('auth:expired', handleTokenExpired as EventListener);
    };
  }, [region]);

  const handleSignIn = (newUser?: User) => {
    // If a new user is provided (from successful Google sign-in), use that
    if (newUser) {
      setUser(newUser);
    } else {
      // Otherwise, check for stored user (for initial load only)
      const storedUser = getStoredUser();
      if (storedUser) {
        setUser(storedUser);
      }
    }
  };

  const handleSignOut = () => {
    clearAllAuthState();
    setUser(null);
  };

  // Determine theme classes based on region
  const regionTheme = region === 'nj' ? 'nj' : 'desert';

  useEffect(() => {
    // Set up token expiration check every 5 minutes
    const cleanup = setupTokenExpirationCheck(5 * 60 * 1000);

    return () => {
      cleanup();
    };
  }, []);

  useEffect(() => {
    async function fetchStripeStatus() {
      if (user && user.token) {
        try {
          const res = await fetch('/api/stripe/account-status', {
            headers: { Authorization: `Bearer ${user.token}` }
          });
          if (res.ok) {
            const data = await res.json();
            setStripeStatus({ payouts_enabled: data.payouts_enabled });
          } else {
            setStripeStatus(undefined);
          }
        } catch (error) {
          setStripeStatus(undefined);
        }
      } else {
        setStripeStatus(undefined);
      }
    }
    fetchStripeStatus();
  }, [user]);

  return (
    <Elements stripe={stripePromise}>
      <StripeStatusContext.Provider value={stripeStatus}>
        <ThemeProvider>
          <div className={`min-h-screen bg-${regionTheme}-background dark:bg-${regionTheme}-background-dark dark:text-${regionTheme}-text-dark pb-20 relative`}>
            <Header
              user={user}
              region={region}
              onSignInClick={() => setIsAuthModalOpen(true)}
              onSignOutClick={handleSignOut}
            />
            <SubscriptionBanner region={region} user={user} />
            <main>
              <SearchSection onSearch={setSearchFilters} region={region} rides={rides}/>
              <UpcomingRides
                filters={searchFilters}
                isSignedIn={!!user}
                region={region}
              />

            </main>

            <FeedbackForm />
            <Footer region={region} />

            <AuthModal
              isOpen={isAuthModalOpen}
              onClose={() => setIsAuthModalOpen(false)}
              onSignIn={handleSignIn}
              onSwitchToRegister={() => {
                setIsAuthModalOpen(false);
                setIsRegisterModalOpen(true);
              }}
            />

            <RegisterModal
              isOpen={isRegisterModalOpen}
              onClose={() => setIsRegisterModalOpen(false)}
              onSignIn={handleSignIn}
            />

            <ReviewModal
              isOpen={isReviewModalOpen}
              onClose={async () => {
                setIsReviewModalOpen(false);
                // Refresh pending reviews after submission
                if (user) {
                  try {
                    const passengerReviews = await getPendingPassengerReviews(user.id);
                    const driverReviews = await getPendingDriverReviews(user.id);
                    setPendingReviews({
                      passenger: passengerReviews,
                      driver: driverReviews,
                    });
                  } catch (error) {
                    console.error("Error refreshing pending reviews:", error);
                  }
                }
              }}
              pendingReviews={pendingReviews}
            />

            {(pendingReviews.passenger.length > 0 || pendingReviews.driver.length > 0) && (
              <button
                onClick={() => setIsReviewModalOpen(true)}
                className="
                    fixed bottom-16 right-6
                    flex items-center space-x-2
                    bg-gradient-to-r from-orange-500 to-orange-600
                    text-white font-medium
                    px-5 py-3 rounded-full
                    shadow-lg
                    hover:from-orange-600 hover:to-orange-700
                    focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-400
                    transition-transform transform hover:scale-105
                  "
              >
                <Bell className="w-5 h-5" />
                <span>
                  You have {pendingReviews.passenger.length + pendingReviews.driver.length} review
                  {(pendingReviews.passenger.length + pendingReviews.driver.length) > 1 ? "s" : ""} due
                </span>
              </button>
            )}
          </div>
        </ThemeProvider>
      </StripeStatusContext.Provider>
    </Elements>
  );
}

export default App;
