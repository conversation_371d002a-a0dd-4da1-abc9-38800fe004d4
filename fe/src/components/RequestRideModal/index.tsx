import React, { useState } from 'react';
import { Modal } from '../Modal';
import { createRideRequestForDriver } from '../../lib/api';
import { getStoredUser } from '../../lib/auth';
import { Loader } from '../Loader';
import styles from './RequestRideModal.module.css';
import { AuthModal } from '../AuthModal';
import { RegisterModal } from '../RegisterModal';
import { User } from '../../types';
import { getLocations } from '../../lib/locations';

import { useJsApiLoader } from '@react-google-maps/api';

type RequestRideModalProps = {
  isOpen: boolean;
  onClose: () => void;
};

// Get current region from localStorage or URL
const getCurrentRegion = (): string => {
  // Try to get region from URL path first
  const pathMatch = window.location.pathname.match(/^\/(cornell|nj)/);
  if (pathMatch && pathMatch[1]) {
    return pathMatch[1];
  }

  // Fallback to stored region or default
  return localStorage.getItem('current_region') || 'cornell';
};

export function RequestRideModal({ isOpen, onClose }: RequestRideModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [isRegisterModalOpen, setIsRegisterModalOpen] = useState(false);
  const [user, setUser] = useState<User | null>(null);

  const [pickupLocation, setPickupLocation] = useState('');
  const [dropoffLocation, setDropoffLocation] = useState('');
  const [pickupCoords, setPickupCoords] = useState<{ lat: number, lng: number } | null>(null);
  const [dropoffCoords, setDropoffCoords] = useState<{ lat: number, lng: number } | null>(null);

  const { isLoaded } = useJsApiLoader({
    googleMapsApiKey: import.meta.env.VITE_GOOGLE_MAPS_API_KEY || '',
    libraries: ['places'],
  });

  const pickupRef = React.useRef<google.maps.places.Autocomplete | null>(null);
  const dropoffRef = React.useRef<google.maps.places.Autocomplete | null>(null);

  // Get region from localStorage or URL
  const region = getCurrentRegion();

  // Get locations for the current region
  const locations = getLocations(region);

  const [requestDetails, setRequestDetails] = useState({
    from: '',
    to: '',
    date: '',
    time: '',
    seatsNeeded: 1,
    suitcasesNeeded: 1,
    notes: [] as string[],
  });

  // Function to update the first note or create it if it doesn't exist
  const updateNote = (noteText: string) => {
    const updatedNotes = [...requestDetails.notes];
    if (updatedNotes.length === 0) {
      updatedNotes.push(noteText);
    } else {
      updatedNotes[0] = noteText;
    }

    setRequestDetails({
      ...requestDetails,
      notes: updatedNotes
    });
  };

  const handleRemoveNote = (index: number) => {
    setRequestDetails({
      ...requestDetails,
      notes: requestDetails.notes.filter((_, i) => i !== index)
    });
  };

  const handleSignIn = () => {
    const updatedUser = getStoredUser();
    setUser(updatedUser);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const user = getStoredUser();
    if (!user) {
      setError('User not authenticated');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    // Make sure field names match what the backend expects
    const requestData = {
      from: requestDetails.from,
      to: requestDetails.to,
      date: requestDetails.date,
      time: requestDetails.time,
      seatsNeeded: requestDetails.seatsNeeded,
      suitcasesNeeded: requestDetails.suitcasesNeeded,
      notes: requestDetails.notes,
      userId: user.id,
      preferredPickupAddress: pickupLocation,
      preferredPickupLat: pickupCoords?.lat ?? null,
      preferredPickupLng: pickupCoords?.lng ?? null,
      preferredDropoffAddress: dropoffLocation,
      preferredDropoffLat: dropoffCoords?.lat ?? null,
      preferredDropoffLng: dropoffCoords?.lng ?? null,
    };

    try {
      const response = await createRideRequestForDriver(requestData);
      setRequestDetails({
        from: '',
        to: '',
        date: '',
        time: '',
        seatsNeeded: 1,
        suitcasesNeeded: 1,
        notes: [],
      });

      if (response.status === 401) {
        localStorage.removeItem('revy_user')
        setError('Session Expired. Please log in again.')
        setIsAuthModalOpen(true);
      }

      if (window.refreshRideRequests) {
        window.refreshRideRequests();
      }

      // Call the global refresh function if it exists
      // if (window.refreshRideRequests) {
      //   window.refreshRideRequests();
      // }
      onClose();
    } catch (err: any) {
      if (err.response && err.response.status === 401) {
        localStorage.removeItem('revy_user')
        setError('Session Expired. Please log in again.')
        setIsAuthModalOpen(true);
      }
      console.error("Error details:", err);
      setError('Failed to create ride request. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <div className={styles.container}>
        <h3 className={styles.title}>
          Request a Ride
        </h3>
        {error && (
          <div className={styles.errorMessage}>
            {error}
          </div>
        )}
        <form onSubmit={handleSubmit} className={styles.form}>
          <div>
            <label htmlFor="from" className={styles.label}>
              Departing from
            </label>
            <select
              id="from"
              className={styles.select}
              value={requestDetails.from}
              onChange={(e) => setRequestDetails({ ...requestDetails, from: e.target.value })}
              required
            >
              <option value="">Select location</option>
              {locations.map(location => (
                <option key={location} value={location}>
                  {location.charAt(0).toUpperCase() + location.slice(1)}
                </option>
              ))}
            </select>
          </div>

          {/* Preferred Pick Up Location */}
          {isLoaded && (
            <div>
              <label className={styles.label}>Preferred Pick Up Location</label>
              <div ref={el => {
                if (el && !pickupRef.current) {
                  const input = el.querySelector('input');
                  if (input) {
                    pickupRef.current = new window.google.maps.places.Autocomplete(input);
                    pickupRef.current.addListener('place_changed', () => {
                      const place = pickupRef.current?.getPlace();
                      setPickupLocation(place?.formatted_address || '');
                      setPickupCoords(place?.geometry?.location
                        ? {
                            lat: place.geometry.location.lat(),
                            lng: place.geometry.location.lng(),
                          }
                        : null
                      );
                    });
                  }
                }
              }}>
                <input
                  type="text"
                  className={styles.input}
                  placeholder="Search pick up location..."
                  value={pickupLocation}
                  onChange={e => setPickupLocation(e.target.value)}
                  required
                />
              </div>
            </div>
          )}

          <div>
            <label htmlFor="to" className={styles.label}>
              Destination
            </label>
            <select
              id="to"
              className={styles.select}
              value={requestDetails.to}
              onChange={(e) => setRequestDetails({ ...requestDetails, to: e.target.value })}
              required
            >
              <option value="">Select location</option>
              {locations.map(location => (
                <option key={location} value={location}>
                  {location.charAt(0).toUpperCase() + location.slice(1)}
                </option>
              ))}
            </select>
          </div>

          {/* Preferred Drop Off Location */}
          {isLoaded && (
            <div>
              <label className={styles.label}>Preferred Drop Off Location</label>
              <div ref={el => {
                if (el && !dropoffRef.current) {
                  const input = el.querySelector('input');
                  if (input) {
                    dropoffRef.current = new window.google.maps.places.Autocomplete(input);
                    dropoffRef.current.addListener('place_changed', () => {
                      const place = dropoffRef.current?.getPlace();
                      setDropoffLocation(place?.formatted_address || '');
                      setDropoffCoords(place?.geometry?.location
                        ? {
                            lat: place.geometry.location.lat(),
                            lng: place.geometry.location.lng(),
                          }
                        : null
                      );
                    });
                  }
                }
              }}>
                <input
                  type="text"
                  className={styles.input}
                  placeholder="Search drop off location..."
                  value={dropoffLocation}
                  onChange={e => setDropoffLocation(e.target.value)}
                  required
                />
              </div>
            </div>
          )}

          <div className={styles.gridContainer}>
            <div>
              <label htmlFor="date" className={styles.label}>
                Date
              </label>
              <input
                type="date"
                id="date"
                className={styles.input}
                value={requestDetails.date}
                onChange={(e) => setRequestDetails({ ...requestDetails, date: e.target.value })}
                min={new Date().toLocaleDateString(
                  "en-US",
                  {
                    year: "numeric",
                    month: "short",
                    day: "2-digit",
                    timeZone: "UTC",
                  }
                )}
                required
              />
            </div>

            <div>
              <label htmlFor="time" className={styles.label}>
                Time
              </label>
              <input
                type="time"
                id="time"
                className={styles.input}
                value={requestDetails.time}
                onChange={(e) => setRequestDetails({ ...requestDetails, time: e.target.value })}
                required
              />
            </div>
          </div>

          <div className={styles.gridContainer}>
            <div>
              <label htmlFor="seatsNeeded" className={styles.label}>
                Seats Needed
              </label>
              <input
                type="number"
                id="seatsNeeded"
                min="1"
                max="8"
                className={styles.input}
                value={requestDetails.seatsNeeded}
                onChange={(e) => setRequestDetails({ ...requestDetails, seatsNeeded: parseInt(e.target.value) })}
                required
              />
            </div>

            <div>
              <label htmlFor="suitcasesNeeded" className={styles.label}>
                Suitcases Needed
              </label>
              <input
                type="number"
                id="suitcasesNeeded"
                min="1"
                max="8"
                className={styles.input}
                value={requestDetails.suitcasesNeeded}
                onChange={(e) => setRequestDetails({ ...requestDetails, suitcasesNeeded: parseInt(e.target.value) })}
                required
              />
            </div>
          </div>
          
          <div>
            <label className={styles.label}>
              Notes
            </label>
            <textarea
              value={requestDetails.notes[0] || ''}
              onChange={(e) => updateNote(e.target.value)}
              placeholder="Add additional notes about your request..."
              className={styles.input}
              rows={3}
            />
          </div>

          <div className={styles.submitContainer}>
            <button
              type="submit"
              disabled={isSubmitting}
              className={`${styles.submitButton} ${isSubmitting
                ? styles.submitButtonDisabled
                : styles.submitButtonEnabled
                }`}
            >
              {isSubmitting ? (
                <>
                  <Loader size="sm" className={styles.loaderCustom} />
                  <span>Submitting...</span>
                </>
              ) : (
                'Submit Request'
              )}
            </button>
          </div>
        </form>
      </div>
      <RegisterModal
        isOpen={isRegisterModalOpen}
        onClose={() => setIsRegisterModalOpen(false)}
        onSignIn={handleSignIn}
      />
      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
        onSignIn={handleSignIn}
        onSwitchToRegister={() => {
          setIsAuthModalOpen(false);
          setIsRegisterModalOpen(true);
        }}
      />
    </Modal>

  );
}

