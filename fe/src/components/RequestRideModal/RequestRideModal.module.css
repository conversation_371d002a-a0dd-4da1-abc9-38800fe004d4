.container {
    @apply p-4 sm:p-6;
  }
  
  .title {
    @apply text-lg sm:text-xl font-semibold text-desert-dark dark:text-desert-light mb-3 sm:mb-4;
  }
  
  .errorMessage {
    @apply mb-3 sm:mb-4 p-2 sm:p-3 bg-red-100 text-red-700 rounded-md text-sm;
  }
  
  .form {
    @apply space-y-3 sm:space-y-4;
  }
  
  .label {
    @apply block text-xs sm:text-sm font-medium text-desert-muted mb-1;
  }
  
  .select {
    @apply w-full px-3 py-2 bg-white dark:bg-desert-dark border rounded-md shadow-sm focus:border-desert-primary focus:ring-1 focus:ring-desert-primary text-sm;
    border-color: rgba(var(--desert-muted-rgb), 0.2);
  }
  
  .input {
    @apply w-full px-3 py-2 bg-white dark:bg-desert-dark border rounded-md shadow-sm focus:border-desert-primary focus:ring-1 focus:ring-desert-primary text-sm;
    border-color: rgba(var(--desert-muted-rgb), 0.2);
  }
  
  .gridContainer {
    @apply grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4;
  }
  
  .notesList {
    @apply space-y-2 mb-2;
  }
  
  .noteItem {
    @apply flex items-center justify-between bg-desert-muted p-2 sm:p-3 rounded-md;
    background-color: rgba(var(--desert-muted-rgb), 0.1);
  }
  
  .noteText {
    @apply text-xs sm:text-sm text-desert-dark dark:text-desert-light;
  }
  
  .removeNoteButton {
    @apply text-desert-muted hover:text-desert-primary ml-2;
  }
  
  .noteInputContainer {
    @apply flex gap-2;
  }
  
  .noteInput {
    @apply flex-1 px-3 py-2 bg-white dark:bg-desert-dark border rounded-md shadow-sm focus:border-desert-primary focus:ring-1 focus:ring-desert-primary text-sm;
    border-color: rgba(var(--desert-muted-rgb), 0.2);
  }
  
  .addNoteButton {
    @apply bg-desert-primary text-white px-3 sm:px-4 py-2 rounded-md hover:bg-desert-accent transition-colors text-sm flex-shrink-0;
  }
  
  .submitContainer {
    @apply mt-4 sm:mt-6;
  }
  
  .submitButton {
    @apply w-full bg-desert-primary text-white px-4 py-2 rounded-md transition-colors flex items-center justify-center text-sm;
  }
  
  .submitButtonDisabled {
    @apply opacity-50 cursor-not-allowed;
  }
  
  .submitButtonEnabled {
    @apply hover:bg-desert-accent;
  }
  
  .loaderCustom {
    @apply mr-2 [&>div]:border-t-white [&>div]:border-white/20;
  }
  
  .priceNote {
    @apply text-xs text-desert-muted mt-1 italic;
  }