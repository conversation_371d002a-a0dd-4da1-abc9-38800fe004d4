.container {
  @apply max-w-3xl mx-auto mt-8 md:mt-12 px-4 md:px-0;
}

.header {
  @apply mb-4 md:mb-6;
}

.title {
  @apply text-lg md:text-xl font-semibold text-desert-dark dark:text-desert-light;
}

.subtitle {
  @apply text-sm text-desert-muted mt-1;
}

.ridesList {
  @apply space-y-4;
}

.rideCard {
  @apply bg-white dark:bg-desert-dark rounded-lg border border-desert-muted p-4 md:p-6;
  /* background-color: rgba(var(--desert-dark-rgb), 0.5); */
  position: relative;
  border-color: rgba(var(--desert-muted-rgb), 0.2);
}

.rideHeader {
  @apply flex flex-col sm:flex-row justify-between items-start mb-4;
}

.rideRoute {
  @apply text-base md:text-lg font-semibold text-desert-dark dark:text-desert-light;
}

.postedTime {
  @apply text-xs md:text-sm text-desert-muted;
}

.priceContainer {
  @apply text-left sm:text-right mt-2 sm:mt-0;
}

.price {
  @apply text-lg md:text-xl font-semibold text-desert-dark dark:text-desert-light;
}

.pricePerSeat {
  @apply text-xs md:text-sm text-desert-muted;
}

.reserveButton {
  @apply flex items-center justify-center mt-2 bg-desert-primary dark:bg-desert-primary-dark text-white font-medium px-4 sm:px-6 py-2 rounded-md hover:bg-desert-accent dark:hover:bg-desert-accent-dark transition-colors text-sm w-full sm:w-auto;
  width: 96px;
  margin-left: auto;
  margin-right: auto;
}

.acceptedButton {
  @apply text-white transition-colors;
  background-color: #7ED9A7 !important;
}
.acceptedButton:hover {
  background-color: #5fcf8a !important;
}

.rejectedButton {
  @apply text-white;
  background-color: #FFA3A3 !important;
}

.pendingButton {
  @apply text-white transition-colors hover:bg-desert-accent dark:hover:bg-desert-accent-dark;
  background-color: #EAB308;
}

.detailsContainer {
  @apply space-y-2 text-sm md:text-base;
}

.detailItem {
  @apply flex items-center text-desert-muted;
}

.detailIcon {
  @apply w-4 h-4 mr-2 flex-shrink-0;
}

.driverContainer {
  @apply mt-4 flex items-center;
}

.driverAvatar {
  @apply w-8 h-8 rounded-full flex items-center justify-center text-white font-medium flex-shrink-0;
}

.passengerAvatar {
  @apply w-8 h-8 rounded-full flex items-center justify-center text-white font-medium flex-shrink-0;
}

.driverInfo {
  @apply ml-3;
}

.driverName {
  @apply text-sm font-medium text-desert-dark dark:text-desert-light;
}

.passengerName {
  @apply text-sm font-medium text-desert-dark dark:text-desert-light;
}

.driverRole {
  @apply text-xs text-desert-muted;
}

.passengerInfo {
  @apply text-xs text-desert-muted;
}

.signInPrompt {
  @apply text-sm text-desert-muted;
}

.signInLink {
  @apply text-desert-primary hover:text-desert-accent;
}

.loadingContainer {
  @apply max-w-3xl mx-auto mt-8 md:mt-12 flex flex-col items-center justify-center px-4 md:px-0;
}

.loadingText {
  @apply text-desert-muted;
}

.errorContainer {
  @apply max-w-3xl mx-auto mt-8 md:mt-12 text-center px-4 md:px-0;
}

.errorText {
  @apply text-red-500;
}

.notesSection {
  @apply mt-3 sm:mt-4;
}

.notesList {
  @apply space-y-2;
}

.noteItem {
  @apply bg-desert-muted p-2 sm:p-3 rounded-md text-xs sm:text-sm;
  background-color: rgba(var(--desert-muted-rgb), 0.1);
}

.cornellBadge {
  @apply inline-block font-bold text-[0.78em] rounded-[0.7em] py-[0.08em] px-[0.5em];
  background-color: #ffe5e5;
  color: #be2323;
  letter-spacing: 0.01em;
  box-shadow: 0 1px 4px rgba(190, 35, 35, 0.07);
}

.requestsList {
  @apply space-y-4;
}

.requestCard {
  @apply bg-white rounded-lg shadow-sm p-4 border;
  border-color: rgba(var(--desert-muted-rgb), 0.1);
}

.requestHeader {
  @apply flex justify-between items-start mb-4;
}

.requestRoute {
  @apply text-lg font-medium text-desert-dark dark:text-desert-light;
}

.pickupAddress {
  @apply text-sm md:text-base font-semibold;
  color: #1f39ad;
}

.dropoffAddress {
  @apply text-sm md:text-base font-semibold mb-1;
  color: #1f39ad;
}

.priceIcon {
  @apply w-4 h-4 mr-1;
}

.detailsButton {
  @apply bg-desert-primary text-white px-3 py-1.5 rounded-md text-sm hover:bg-desert-accent transition-colors;
}

.passengerContainer {
  @apply flex items-center mt-4 pt-4 border-t;
  border-color: rgba(var(--desert-muted-rgb), 0.1);
}

.anonymousPassenger {
  @apply text-sm text-desert-muted;
}

.emptyState {
  @apply text-center py-12 text-desert-muted;
}

/* Modal Styles */
.modalContainer {
  @apply p-4 sm:p-6 max-w-lg mx-auto;
}

.modalTitle {
  @apply text-xl font-semibold text-desert-dark dark:text-desert-light mb-4;
}

.modalRouteContainer {
  @apply mb-6;
}

.modalRoute {
  @apply text-lg font-medium text-desert-dark dark:text-desert-light;
}

.modalPostedTime {
  @apply text-xs text-desert-muted mt-1;
}

.modalDetailsGrid {
  @apply grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6;
}

.modalDetailItem {
  @apply flex items-start;
}

.modalDetailIcon {
  @apply w-5 h-5 mr-3 text-desert-primary mt-0.5;
}

.modalDetailLabel {
  @apply text-xs text-desert-muted;
}

.modalDetailValue {
  @apply text-sm font-medium text-desert-dark dark:text-desert-light;
}

.modalSectionTitle {
  @apply text-sm font-medium text-desert-dark dark:text-desert-light mb-2;
}

.modalNotesSection {
  @apply mb-6;
}

.modalNotesList {
  @apply space-y-2;
}

.modalNoteItem {
  @apply bg-desert-light dark:bg-desert-dark p-3 rounded-md text-sm;
}

.modalPassengerSection {
  @apply mb-6;
}

.modalPassengerInfo {
  @apply flex items-center;
}

.modalPassengerAvatar {
  @apply w-10 h-10 rounded-full flex items-center justify-center text-white mr-3 text-sm font-medium;
}

.modalPassengerName {
  @apply text-sm font-medium text-desert-dark dark:text-desert-light;
}

.modalPassengerStatus {
  @apply text-xs text-desert-muted;
}

.modalActionSection {
  @apply mt-6;
}

.modalContactButton {
  @apply w-full bg-desert-primary text-white py-2 px-4 rounded-md flex items-center justify-center hover:bg-desert-accent transition-colors;
}

.modalButtonIcon {
  @apply w-5 h-5 mr-2;
}

.modalOwnRequestMessage {
  @apply mt-6 p-3 bg-desert-light dark:bg-desert-dark rounded-md text-sm text-center;
}

.deleteButton {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  color: #e53e3e;
  border: none;
  border-radius: 4px;
  padding: 4px;
  margin-left: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.deleteButton:hover {
  background-color: rgba(229, 62, 62, 0.1);
}

.deleteIcon {
  width: 16px;
  height: 16px;
}

.cancelRideButton {
  @apply px-4 py-2 bg-red-500 text-white text-sm font-medium rounded hover:bg-red-600 transition-colors duration-200;
  position: absolute;
  bottom: 1rem;
  right: 1rem;
}

/* Styling for mobile devices */
@media (max-width: 640px) {
  .cancelRideButton {
    bottom: 0.75rem;
    right: 0.75rem;
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
  }
}
.ithacaBadge {
  @apply inline-block font-bold text-[0.78em] rounded-[0.7em] py-[0.08em] px-[0.5em];
  background-color: #fcda8a;
  color: #003c71;
  letter-spacing: 0.01em;
  box-shadow: 0 1px 4px rgba(190, 174, 35, 0.07);
}

.syracuseBadge {
  @apply inline-block font-bold text-[0.78em] rounded-[0.7em] py-[0.08em] px-[0.5em];
  background-color: #fca059;
  color: #000e54;
  letter-spacing: 0.01em;
  box-shadow: 0 1px 4px rgba(38, 35, 190, 0.07);
}

.binghamtonBadge {
  @apply inline-block font-bold text-[0.78em] rounded-[0.7em] py-[0.08em] px-[0.5em];
  background-color: #a8d6cb;
  color: #005a43;
  letter-spacing: 0.01em;
  box-shadow: 0 1px 4px rgba(35, 190, 110, 0.07);
}
