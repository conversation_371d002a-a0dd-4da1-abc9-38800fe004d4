import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Calendar, Clock, Users, Lock, Briefcase, DollarSign, MessageSquare } from "lucide-react";
import { Modal } from "../Modal";
import { SearchFilters } from "../SearchSection";
import { SignInModal } from "../SignInModal";
import { RideRequest } from "../../types";
import { getAllRideRequests } from "../../lib/api";
import { getStoredUser } from "../../lib/auth";
import { Loader } from "../Loader";
import styles from "./ActiveRideRequests.module.css";

const formatDisplayName = (fullName: string): string => {
  return fullName.split(' ')[0]; // Get only the first name
};

type ActiveRideRequestsProps = {
  filters: SearchFilters;
  isSignedIn: boolean;
  region: string;
  rideRequests: RideRequest[];
  error: string | null;
  isLoading: boolean;

};

export function ActiveRideRequests({ filters, isSignedIn, region, rideRequests, error, isLoading }: ActiveRideRequestsProps) {
  const navigate = useNavigate();
  const [isSignInModalOpen, setIsSignInModalOpen] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState<RideRequest | null>(null);
  //const [rideRequests, setRideRequests] = useState<RideRequest[]>([]);
  const User = getStoredUser();

  /*
  const fetchRideRequests = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const apiRideRequests = await getAllRideRequests({
        from: filters.from || undefined,
        to: filters.to || undefined,
        date: filters.date || undefined,
      });
      setRideRequests(apiRideRequests);
    } catch (err) {
      setError("Failed to fetch ride requests. Please try again later.");
      console.error("Error fetching ride requests:", err);
    } finally {
      setIsLoading(false);
    }
  };*/


  useEffect(() => {
    //fetchRideRequests();
  }, [filters.from, filters.to, filters.date]);

  const formatLocation = (location: string): string => {
    return location
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  };

  const handleDetailsClick = (request: RideRequest) => {
    if (!isSignedIn) {
      setIsSignInModalOpen(true);
    } else {
      setSelectedRequest(request);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString + "T00:00:00").toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "2-digit",
      timeZone: "UTC",
    });
  };

  const handleContactClick = async (passengerId: string) => {
    // This would be implemented to contact the passenger
    // For now, just show a message
    navigate(`/${region}/conversation-messages/${passengerId}`);

  };

  if (isLoading) {
    return (
      <div className={styles.loadingContainer}>
        <Loader size="lg" className="mb-4" />
        <p className={styles.loadingText}>Loading ride requests...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.errorContainer}>
        <p className={styles.errorText}>{error}</p>
      </div>
    );
  }

  return (
    <div>
      <div className={styles.requestsList}>
        {rideRequests?.length === 0 ? (
          <div className={styles.emptyState}>
            <p>No ride requests match your search criteria.</p>
          </div>
        ) : (
          rideRequests?.map((request) => (
            <div
              key={request.id}
              className={styles.requestCard}
            >
              <div className={styles.requestHeader}>
                <div>
                  <div className={styles.requestRoute}>
                    {formatLocation(request.from)} → {formatLocation(request.to)}
                  </div>
                  {request.preferredPickupAddress && (
                    <div className={styles.pickupAddress}>
                      <span>Preferred Pick Up: </span>
                      <span>{request.preferredPickupAddress}</span>
                    </div>
                  )}
                  {request.preferredDropoffAddress && (
                    <div className={styles.dropoffAddress}>
                      <span>Preferred Drop Off: </span>
                      <span>{request.preferredDropoffAddress}</span>
                    </div>
                  )}
                  <div className={styles.postedTime}>
                    Posted {request.postedTime || "recently"}
                  </div>
                </div>
                <div className={styles.priceContainer}>
                  <button
                    className={styles.detailsButton}
                    onClick={() => handleDetailsClick(request)}
                  >
                    Details
                  </button>
                </div>
              </div>

              <div className={styles.detailsContainer}>
                <div className={styles.detailItem}>
                  <Calendar className={styles.detailIcon} />
                  <span>{formatDate(request.date)}</span>
                </div>
                <div className={styles.detailItem}>
                  <Clock className={styles.detailIcon} />
                  <span>Departing at {request.time}</span>
                </div>
                <div className={styles.detailItem}>
                  <Users className={styles.detailIcon} />
                  <span>{request.seatsNeeded} {request.seatsNeeded === 1 ? "seat" : "seats"} needed</span>
                </div>
                <div className={styles.detailItem}>
                  <Briefcase className={styles.detailIcon} />
                  <span>{request.suitcasesNeeded} {request.suitcasesNeeded === 1 ? "suitcase" : "suitcases"} needed</span>
                </div>
              </div>

              {request.notes && request.notes.length > 0 && (
                <div className={styles.notesSection}>
                  <div className={styles.notesList}>
                    {request.notes.map((note, index) => (
                      <div key={index} className={styles.noteItem}>
                        {note}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className={styles.passengerContainer}>
                {request.passenger ? (
                  <>
                    <div
                      className={`${styles.passengerAvatar} ${request.passenger.avatarColor || 'bg-desert-primary'}`}
                      onClick={() => isSignedIn && navigate(`/${region}/profile/${request.passenger.id}`)}
                      style={{ cursor: isSignedIn ? 'pointer' : 'default' }}
                    >
                      {isSignedIn ? (
                        request.passenger.avatar ? (
                          <img
                            src={request.passenger.avatar}
                            alt={request.passenger.name}
                            className="w-full h-full rounded-full object-cover"
                          />
                        ) : (
                          request.passenger.name[0]
                        )
                      ) : (
                        <Lock className="w-4 h-4" />
                      )}
                    </div>
                    <div className={styles.passengerInfo}>
                      {isSignedIn ? (
                        <div
                          className={styles.passengerName}
                          onClick={() => navigate(`/${region}/profile/${request.passenger.id}`)}
                          style={{ cursor: 'pointer' }}
                        >
                          {formatDisplayName(request.passenger.name)}
                        </div>
                      ) : (
                        <div className={styles.signInPrompt}>
                          <button
                            className={styles.signInLink}
                            onClick={() => setIsSignInModalOpen(true)}
                          >
                            Sign in
                          </button>{" "}
                          to view passenger details
                        </div>
                      )}
                    </div>
                  </>
                ) : (
                  <div className={styles.anonymousPassenger}>
                    {isSignedIn ? (
                      "Anonymous passenger"
                    ) : (
                      <div className={styles.signInPrompt}>
                        <button
                          className={styles.signInLink}
                          onClick={() => setIsSignInModalOpen(true)}
                        >
                          Sign in
                        </button>{" "}
                        to view passenger details
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          ))
        )}
      </div>

      <SignInModal
        isOpen={isSignInModalOpen}
        onClose={() => setIsSignInModalOpen(false)}
        onSignInClick={() => {
          window.dispatchEvent(new CustomEvent('open-auth-modal'));
        }}
      />

      {selectedRequest && (
        <Modal isOpen={!!selectedRequest} onClose={() => setSelectedRequest(null)}>
          <div className={styles.modalContainer}>
            <h3 className={styles.modalTitle}>
              Ride Request Details
            </h3>

            <div className={styles.modalRouteContainer}>
              <div className={styles.modalRoute}>
                {formatLocation(selectedRequest.from)} → {formatLocation(selectedRequest.to)}
              </div>
              <div className={styles.modalPostedTime}>
                Posted {selectedRequest.postedTime || 'recently'}
              </div>
            </div>

            <div className={styles.modalDetailsGrid}>
              <div className={styles.modalDetailItem}>
                <Calendar className={styles.modalDetailIcon} />
                <div>
                  <div className={styles.modalDetailLabel}>Date</div>
                  <div className={styles.modalDetailValue}>{formatDate(selectedRequest.date)}</div>
                </div>
              </div>

              <div className={styles.modalDetailItem}>
                <Clock className={styles.modalDetailIcon} />
                <div>
                  <div className={styles.modalDetailLabel}>Time</div>
                  <div className={styles.modalDetailValue}>{selectedRequest.time}</div>
                </div>
              </div>

              <div className={styles.modalDetailItem}>
                <Users className={styles.modalDetailIcon} />
                <div>
                  <div className={styles.modalDetailLabel}>Seats Needed</div>
                  <div className={styles.modalDetailValue}>{selectedRequest.seatsNeeded}</div>
                </div>
              </div>

              <div className={styles.modalDetailItem}>
                <Briefcase className={styles.modalDetailIcon} />
                <div>
                  <div className={styles.modalDetailLabel}>Suitcases</div>
                  <div className={styles.modalDetailValue}>{selectedRequest.suitcasesNeeded}</div>
                </div>
              </div>

            </div>

            {selectedRequest.notes && selectedRequest.notes.length > 0 && (
              <div className={styles.modalNotesSection}>
                <h4 className={styles.modalSectionTitle}>Notes</h4>
                <div className={styles.modalNotesList}>
                  {selectedRequest.notes.map((note, index) => (
                    <div key={index} className={styles.modalNoteItem}>
                      {note}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {selectedRequest.passenger && (
              <div className={styles.modalPassengerSection}>
                <h4 className={styles.modalSectionTitle}>Passenger</h4>
                <div className={styles.modalPassengerInfo}>
                  <div className={`${styles.modalPassengerAvatar} ${selectedRequest.passenger.avatarColor || 'bg-desert-primary'}`}>
                    {selectedRequest.passenger.name[0]}
                  </div>
                  <div>
                    <div className={styles.modalPassengerName}>{selectedRequest.passenger.name}</div>
                    <div className={styles.modalPassengerStatus}>Cornell Verified Passenger</div>
                  </div>
                </div>
              </div>
            )}

            {User?.id !== selectedRequest.passenger?.id && (
              <div className={styles.modalActionSection}>
                <button
                  onClick={() => {
                    handleContactClick(selectedRequest.passenger.id);
                  }}
                  className={styles.modalContactButton}
                >
                  <MessageSquare className={styles.modalButtonIcon} />
                  <span>Contact Passenger</span>
                </button>
              </div>
            )}

            {User?.id === selectedRequest.passenger?.id && (
              <div className={styles.modalOwnRequestMessage}>
                This is your ride request.
              </div>
            )}
          </div>
        </Modal>
      )}
    </div>
  );
}
