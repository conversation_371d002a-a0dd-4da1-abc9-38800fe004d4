.container {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6;
}

.header {
  @apply mb-6;
}

.title {
  @apply text-xl font-semibold text-desert-dark dark:text-desert-light;
}

.subtitle {
  @apply text-sm text-desert-muted mt-1;
}

.requestsList {
  @apply space-y-4;
}

.requestCard {
  @apply bg-white dark:bg-desert-dark rounded-lg border border-desert-muted p-4 md:p-6;
  /* background-color: rgba(var(--desert-dark-rgb), 0.5); */
  border-color: rgba(var(--desert-muted-rgb), 0.2);
}

.requestHeader {
  @apply flex flex-col sm:flex-row justify-between items-start mb-4;
}

.requestRoute {
  @apply text-base md:text-lg font-semibold text-desert-dark dark:text-desert-light;
}

.pickupAddress {
  @apply text-sm md:text-base font-semibold;
  color: #1f39ad;
}

.dropoffAddress {
  @apply text-sm md:text-base font-semibold mb-1;
  color: #1f39ad;
}

.postedTime {
  @apply text-xs md:text-sm text-desert-muted;
}

.priceContainer {
  @apply text-left sm:text-right mt-2 sm:mt-0;
}

.price {
  @apply text-lg md:text-xl font-semibold text-desert-dark dark:text-desert-light;
}

.priceIcon {
  @apply w-4 h-4 mr-1;
}

.detailsButton {
  @apply mt-2 bg-desert-primary dark:bg-desert-primary-dark text-white px-4 sm:px-6 py-2 rounded-md hover:bg-desert-accent dark:hover:bg-desert-accent-dark transition-colors text-sm w-full sm:w-auto;
}

.detailsContainer {
  @apply space-y-2 text-sm md:text-base;
}

.detailItem {
  @apply flex items-center text-desert-muted;
}

.detailIcon {
  @apply w-4 h-4 mr-2 text-desert-muted;
}

.notesSection {
  @apply mt-3 sm:mt-4;
}

.notesList {
  @apply space-y-2;
}

.noteItem {
  @apply bg-desert-muted p-2 sm:p-3 rounded-md text-xs sm:text-sm;
  background-color: rgba(var(--desert-muted-rgb), 0.1);
}

.passengerContainer {
  @apply flex items-center mt-4 pt-4 border-t;
  border-color: rgba(var(--desert-muted-rgb), 0.1);
}

.passengerAvatar {
  @apply w-8 h-8 rounded-full flex items-center justify-center text-white mr-3;
}

.passengerInfo {
  @apply flex-1;
}

.passengerName {
  @apply text-sm font-medium text-desert-dark dark:text-desert-light;
}

.anonymousPassenger {
  @apply text-sm text-desert-muted;
}

.signInPrompt {
  @apply text-xs text-desert-muted;
}

.signInLink {
  @apply text-desert-primary hover:underline;
}

.loadingContainer {
  @apply flex flex-col items-center justify-center py-12;
}

.loadingText {
  @apply text-desert-muted;
}

.errorContainer {
  @apply bg-red-50 dark:bg-red-900/20 p-4 rounded-md;
}

.errorText {
  @apply text-red-800 dark:text-red-200;
}

.emptyState {
  @apply text-center py-12 text-desert-muted;
}

/* Modal Styles */
.modalContainer {
  @apply p-4 sm:p-6 max-w-lg mx-auto;
}

.modalTitle {
  @apply text-xl font-semibold text-desert-dark dark:text-desert-light mb-4;
}

.modalRouteContainer {
  @apply mb-6;
}

.modalRoute {
  @apply text-lg font-medium text-desert-dark dark:text-desert-light;
}

.modalPostedTime {
  @apply text-xs text-desert-muted mt-1;
}

.modalDetailsGrid {
  @apply grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6;
}

.modalDetailItem {
  @apply flex items-start;
}

.modalDetailIcon {
  @apply w-5 h-5 mr-3 text-desert-primary mt-0.5;
}

.modalDetailLabel {
  @apply text-xs text-desert-muted;
}

.modalDetailValue {
  @apply text-sm font-medium text-desert-dark dark:text-desert-light;
}

.modalSectionTitle {
  @apply text-sm font-medium text-desert-dark dark:text-desert-light mb-2;
}

.modalNotesSection {
  @apply mb-6;
}

.modalNotesList {
  @apply space-y-2;
}

.modalNoteItem {
  @apply bg-desert-light dark:bg-desert-dark p-3 rounded-md text-sm;
}

.modalPassengerSection {
  @apply mb-6;
}

.modalPassengerInfo {
  @apply flex items-center;
}

.modalPassengerAvatar {
  @apply w-10 h-10 rounded-full flex items-center justify-center text-white mr-3 text-sm font-medium;
}

.modalPassengerName {
  @apply text-sm font-medium text-desert-dark dark:text-desert-light;
}

.modalPassengerStatus {
  @apply text-xs text-desert-muted;
}

.modalActionSection {
  @apply mt-6;
}

.modalContactButton {
  @apply w-full bg-desert-primary text-white py-2 px-4 rounded-md flex items-center justify-center hover:bg-desert-accent transition-colors;
}

.modalButtonIcon {
  @apply w-5 h-5 mr-2;
}

.modalOwnRequestMessage {
  @apply mt-6 p-3 bg-desert-light dark:bg-desert-dark rounded-md text-sm text-center;
}

.deleteButton {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  color: #e53e3e;
  border: none;
  border-radius: 4px;
  padding: 4px;
  margin-left: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.deleteButton:hover {
  background-color: rgba(229, 62, 62, 0.1);
}

.deleteIcon {
  width: 16px;
  height: 16px;
}