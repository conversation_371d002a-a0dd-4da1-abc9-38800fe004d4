import React, { useState } from 'react';
import { Modal } from '../Modal';
import { Loader } from '../Loader';
import { X, Plus } from 'lucide-react';
import { createRide } from '../../lib/api';
import { getStoredUser } from '../../lib/auth';
import { getLocations } from '../../lib/locations';
import styles from './PostRideModal.module.css';

import { useJsApiLoader, Autocomplete } from '@react-google-maps/api';

type PostRideModalProps = {
  isOpen: boolean;
  onClose: () => void;
  region: string;
};

const getRandomColor = () => {
  const colors = ['bg-desert-primary', 'bg-desert-secondary', 'bg-desert-accent'];
  return colors[Math.floor(Math.random() * colors.length)];
};

const formatPostedTime = () => 'just now';

export function PostRideModal({ isOpen, onClose, region }: PostRideModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [rideDetails, setRideDetails] = useState({
    from: '',
    to: '',
    date: '',
    time: '',
    seats: 1,
    suitcases: 1,
    price: '',
    notes: [] as string[],
    pickupAddress: '',
    pickupLat: null as number | null,
    pickupLng: null as number | null,
    dropoffAddress: '',
    dropoffLat: null as number | null,
    dropoffLng: null as number | null,
  });

  const { isLoaded } = useJsApiLoader({
    googleMapsApiKey: import.meta.env.VITE_GOOGLE_MAPS_API_KEY || '',
    libraries: ['places'],
  });

  const pickupRef = React.useRef<google.maps.places.Autocomplete | null>(null);
  const dropoffRef = React.useRef<google.maps.places.Autocomplete | null>(null);

  // Function to update the first note or create it if it doesn't exist
  const updateNote = (noteText: string) => {
    const updatedNotes = [...rideDetails.notes];
    if (updatedNotes.length === 0) {
      updatedNotes.push(noteText);
    } else {
      updatedNotes[0] = noteText;
    }

    setRideDetails({
      ...rideDetails,
      notes: updatedNotes
    });
  };

  // Get region-specific locations
  const locations = getLocations(region);

  const handleAddNote = () => {
    if (newNote.trim()) {
      setRideDetails({
        ...rideDetails,
        notes: [...rideDetails.notes, newNote.trim()]
      });
      setNewNote('');
    }
  };

  const handleRemoveNote = (index: number) => {
    setRideDetails({
      ...rideDetails,
      notes: rideDetails.notes.filter((_, i) => i !== index)
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const user = getStoredUser();
    if (!user) {
      setError('User not authenticated');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    const rideData = {
      from: rideDetails.from,
      to: rideDetails.to,
      date: rideDetails.date,
      time: rideDetails.time,
      seatsAvailable: rideDetails.seats,
      totalSeats: rideDetails.seats,
      suitcasesAvailable: rideDetails.suitcases,
      totalSuitcases: rideDetails.suitcases,
      driver: {
        name: user.name,
        info: 'Cornell Verified driver', // Changed from 'Driver' to 'Cornell Verified driver'
        avatarColor: getRandomColor(),
        avatar: user.avatar
      },
      price: Number(rideDetails.price),
      postedTime: formatPostedTime(),
      notes: rideDetails.notes,
      pickupAddress: rideDetails.pickupAddress,
      pickupLat: rideDetails.pickupLat,
      pickupLng: rideDetails.pickupLng,
      dropoffAddress: rideDetails.dropoffAddress,
      dropoffLat: rideDetails.dropoffLat,
      dropoffLng: rideDetails.dropoffLng,
    };

    try {
      await createRide(rideData, user.token);

      setRideDetails({
        from: '',
        to: '',
        date: '',
        time: '',
        seats: 1,
        suitcases: 1,
        price: '',
        notes: [],
        pickupAddress: '',
        pickupLat: null,
        pickupLng: null,
        dropoffAddress: '',
        dropoffLat: null,
        dropoffLng: null,
      });

      // Call the global refresh function
      if (window.refreshRides) {
        window.refreshRides();
        window.refreshMyRides();
      }

      onClose();
    } catch (err) {
      setError('Failed to create ride. Please ensure the time of your ride is in the future and try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <div className={styles.container}>
        <h3 className={styles.title}>
          Post a New Ride
        </h3>
        {error && (
          <div className={styles.errorMessage}>
            {error}
          </div>
        )}
        <form onSubmit={handleSubmit} className={styles.form}>
          <div>
            <label htmlFor="from" className={styles.label}>
              Departing from
            </label>
            <select
              id="from"
              className={styles.select}
              value={rideDetails.from}
              onChange={(e) => setRideDetails({ ...rideDetails, from: e.target.value })}
              required
            >
              <option value="">Select location</option>
              {locations.map(location => (
                <option key={location} value={location}>
                  {location.charAt(0).toUpperCase() + location.slice(1)}
                </option>
              ))}
            </select>
          </div>

          {isLoaded && (
            <div>
              <label className={styles.label}>Pick Up Location</label>
              <div
                ref={el => {
                  if (el) {
                    const input = el.querySelector('input');
                    if (input) {
                      pickupRef.current = new window.google.maps.places.Autocomplete(input);
                      pickupRef.current.addListener('place_changed', () => {
                        const place = pickupRef.current?.getPlace();
                        setRideDetails(prev => ({
                          ...prev,
                          pickupAddress: place?.formatted_address || '',
                          pickupLat: place?.geometry?.location?.lat() ?? null,
                          pickupLng: place?.geometry?.location?.lng() ?? null,
                        }));
                      });
                    }
                  }
                }}
              >
                <input
                  type="text"
                  className={styles.input}
                  placeholder="Search pick up location..."
                  value={rideDetails.pickupAddress}
                  onChange={e => setRideDetails({ ...rideDetails, pickupAddress: e.target.value })}
                  required
                />
              </div>
            </div>
          )}

          <div>
            <label htmlFor="to" className={styles.label}>
              Destination
            </label>
            <select
              id="to"
              className={styles.select}
              value={rideDetails.to}
              onChange={(e) => setRideDetails({ ...rideDetails, to: e.target.value })}
              required
            >
              <option value="">Select location</option>
              {locations.map(location => (
                <option key={location} value={location}>
                  {location.charAt(0).toUpperCase() + location.slice(1)}
                </option>
              ))}
            </select>
          </div>

          {isLoaded && (
            <div>
              <label className={styles.label}>Drop Off Location</label>
              <div
                ref={el => {
                  if (el && !dropoffRef.current) {
                    const input = el.querySelector('input');
                    if (input) {
                      dropoffRef.current = new window.google.maps.places.Autocomplete(input);
                      dropoffRef.current.addListener('place_changed', () => {
                        const place = dropoffRef.current?.getPlace();
                        setRideDetails(prev => ({
                          ...prev,
                          dropoffAddress: place?.formatted_address || '',
                          dropoffLat: place?.geometry?.location?.lat() ?? null,
                          dropoffLng: place?.geometry?.location?.lng() ?? null,
                        }));
                      });
                    }
                  }
                }}
              >
                <input
                  type="text"
                  className={styles.input}
                  placeholder="Search drop off location..."
                  value={rideDetails.dropoffAddress}
                  onChange={e => setRideDetails({ ...rideDetails, dropoffAddress: e.target.value })}
                  required
                />
              </div>
            </div>
          )}

          <div className={styles.gridContainer}>
            <div>
              <label htmlFor="date" className={styles.label}>
                Date
              </label>
              <input
                type="date"
                id="date"
                className={styles.input}
                value={rideDetails.date}
                onChange={(e) => setRideDetails({ ...rideDetails, date: e.target.value })}
                min={new Date().toLocaleDateString(
                  "en-US",
                  {
                    year: "numeric",
                    month: "short",
                    day: "2-digit",
                    timeZone: "UTC",
                  }
                )}
                required
              />
            </div>

            <div>
              <label htmlFor="time" className={styles.label}>
                Time
              </label>
              <input
                type="time"
                id="time"
                className={styles.input}
                value={rideDetails.time}
                onChange={(e) => setRideDetails({ ...rideDetails, time: e.target.value })}
                required
              />
            </div>
          </div>

          <div className={styles.gridContainer}>
            <div>
              <label htmlFor="seats" className={styles.label}>
                Available Seats
              </label>
              <input
                type="number"
                id="seats"
                min="1"
                max="8"
                className={styles.input}
                value={rideDetails.seats}
                onChange={(e) => setRideDetails({ ...rideDetails, seats: parseInt(e.target.value) })}
                required
              />
            </div>

            <div>
              <label htmlFor="suitcases" className={styles.label}>
                Suitcases Allowed
              </label>
              <input
                type="number"
                id="suitcases"
                min="1"
                max="8"
                className={styles.input}
                value={rideDetails.suitcases}
                onChange={(e) => setRideDetails({ ...rideDetails, suitcases: parseInt(e.target.value) })}
                required
              />
            </div>
          </div>

          <div>
            <label htmlFor="price" className={styles.label}>
              Price per Seat ($)
            </label>
            <input
              type="number"
              id="price"
              min="1"
              className={styles.input}
              value={rideDetails.price}
              onChange={(e) => setRideDetails({ ...rideDetails, price: e.target.value })}
              required
            />
            <p className={styles.priceNote}>
              Posting rides under $40 helps more students join.
            </p>
          </div>

          <div>
            <label className={styles.label}>
              Notes
            </label>
            <textarea
              value={rideDetails.notes[0] || ''}
              onChange={(e) => updateNote(e.target.value)}
              placeholder="Add additional notes about your trip..."
              className={styles.input}
              rows={3}
            />
          </div>

          <div className={styles.submitContainer}>
            <button
              type="submit"
              disabled={isSubmitting}
              className={`${styles.submitButton} ${isSubmitting
                ? styles.submitButtonDisabled
                : styles.submitButtonEnabled
                }`}
            >
              {isSubmitting ? (
                <>
                  <Loader size="sm" className={styles.loaderCustom} />
                  <span>Posting...</span>
                </>
              ) : (
                'Post Ride'
              )}
            </button>
          </div>
        </form>
      </div>
    </Modal>
  );
}
