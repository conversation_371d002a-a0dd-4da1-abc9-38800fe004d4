.container {
  max-width: 36rem;
  margin: 2rem auto 0;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.container:global(.dark) {
  background-color: #111827;
  border-color: #374151;
}

.title {
  font-size: 1.125rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-align: center;
}

.description {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 1rem;
  text-align: center;
}

.description:global(.dark) {
  color: #9ca3af;
}

.dummyEmail {
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
}

.formGroup {
  margin-bottom: 1rem;
}

.textarea {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
  min-height: 5rem;
  resize: vertical;
  background-color: #f9fafb;
}

.textarea:global(.dark) {
  border-color: #374151;
  background-color: #1f2937;
  color: white;
}

.textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
  background-color: #f9fafb;
}

.input:global(.dark) {
  border-color: #374151;
  background-color: #1f2937;
  color: white;
}

.input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.error {
  color: #ef4444;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
  text-align: center;
}

.submitButton {
  width: 100%;
  background-color: #f4a259;
  color: white;
  font-weight: 600;
  padding: 0.5rem 0;
  border-radius: 0.25rem;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s ease-in-out;
}

.submitButton:hover {
  background-color: rgba(244, 162, 89, 0.9);
}

.submitButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.successMessage {
  background-color: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 0.25rem;
  padding: 1rem;
  text-align: center;
  margin-top: 2rem;
}

.successTitle {
  color: #15803d;
  font-weight: 600;
}

.successText {
  color: #16a34a;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}
