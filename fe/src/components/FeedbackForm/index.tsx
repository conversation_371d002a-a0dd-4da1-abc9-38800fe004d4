import { useState, useEffect } from 'react';
import type { User } from '../../types';

type FeedbackFormProps = {
  user?: User | null;
};

export function FeedbackForm({ user }: FeedbackFormProps) {
  const [comment, setComment] = useState('');
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);
  const [error, setError] = useState('');

  // Pre-populate email when user is logged in
  useEffect(() => {
    if (user?.email) {
      setEmail(user.email);
    }
  }, [user]);

  const DUMMY_EMAIL = '<EMAIL>'; // Replace with real email later

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError('');
    try {
      // Simulate sending feedback (replace with real API/email logic)
      await new Promise((resolve) => setTimeout(resolve, 1000));
      // Optionally, POST to a placeholder endpoint here
      setSubmitted(true);
    } catch (err) {
      console.error('Error sending feedback:', err);
      setError('Failed to send feedback. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (submitted) {
    return (
      <div className="bg-green-50 border border-green-200 rounded p-4 text-center mt-8">
        <p className="text-green-700 font-semibold">Thank you for your feedback!</p>
        <p className="text-green-600 text-sm mt-1">We appreciate your input and will use it to improve Kamel Ride.</p>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="max-w-xl mx-auto bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg p-6 mt-8 shadow">
      <h2 className="text-lg font-bold mb-2 text-center">Feedback</h2>
      <p className="text-sm text-gray-600 dark:text-gray-300 mb-4 text-center">Share your experience with Kamel Ride. Your feedback goes to <span className="font-mono">{DUMMY_EMAIL}</span>.</p>
      <div className="mb-4">
        <textarea
          className="w-full p-2 border border-gray-300 dark:border-gray-700 rounded min-h-[80px] resize-vertical bg-gray-50 dark:bg-gray-800"
          placeholder="Your comments..."
          value={comment}
          onChange={e => setComment(e.target.value)}
          required
          maxLength={1000}
        />
      </div>
      <div className="mb-4">
        <input
          type="email"
          className="w-full p-2 border border-gray-300 dark:border-gray-700 rounded bg-gray-50 dark:bg-gray-800"
          placeholder={user ? "Email (from your account)" : "Your email (optional)"}
          value={email}
          onChange={e => setEmail(e.target.value)}
        />
      </div>
      {error && <div className="text-red-500 text-sm mb-2 text-center">{error}</div>}
      <button
        type="submit"
        className="w-full bg-desert-primary text-white font-semibold py-2 rounded hover:bg-desert-primary/90 transition-colors disabled:opacity-60"
        disabled={isSubmitting || !comment.trim()}
      >
        {isSubmitting ? 'Sending...' : 'Submit Feedback'}
      </button>
    </form>
  );
}

export default FeedbackForm; 