import { useState, useEffect } from "react";
import styles from './SearchSection.module.css';

type SingleDate = {
  date: string;
  label: string;
  rideCount?: number;
};

type DateRangeCarouselProps = {
  selectedDate: SingleDate | null;
  onDateSelect: (date: SingleDate) => void;
  visibleCount?: number;
  rides?: any[];
};

// Helper to parse YYYY-MM-DD as local date (no timezone offset)
function parseLocalDate(dateStr: string) {
  const [year, month, day] = dateStr.split('-').map(Number);
  return new Date(year, month - 1, day);
}

function addDays(date: Date, days: number) {
  const d = new Date(date);
  d.setDate(d.getDate() + days);
  return d;
}

function formatDate(date: Date) {
  // Always format as YYYY-MM-DD in local time
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
}

function formatLabel(date: Date) {
  return date.getDate() + " " + date.toLocaleString("default", { month: "short" });
}

function isSameDay(a: Date, b: Date) {
  return (
    a.getFullYear() === b.getFullYear() &&
    a.getMonth() === b.getMonth() &&
    a.getDate() === b.getDate()
  );
}

export function DateRangeCarousel({
  selectedDate,
  onDateSelect,
  visibleCount = 5,
  rides = [],
}: DateRangeCarouselProps) {
  // Always use local date for today
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const [startDate, setStartDate] = useState(() => new Date(today));

  // Helper function to count rides for a specific date
  function getRideCountForDate(dateStr: string): number {
    return rides.filter(ride => ride.date === dateStr).length;
  }

  // Helper function
  function getDates(start: Date, count: number) {
    const arr: SingleDate[] = [];
    for (let i = 0; i < count; i++) {
      const d = addDays(start, i);
      const dateStr = formatDate(d);
      arr.push({ 
        date: dateStr, 
        label: formatLabel(d),
        rideCount: getRideCountForDate(dateStr)
      });
    }
    return arr;
  }

  // Generate only visibleCount dates
  const [dates, setDates] = useState<SingleDate[]>(getDates(startDate, visibleCount));

  // Update dates when startDate or visibleCount or rides changes
  useEffect(() => {
    setDates(getDates(startDate, visibleCount));
  }, [startDate, visibleCount, rides]);

  // If selectedDate changes (from input), and is outside current range, shift carousel to include it
  useEffect(() => {
    if (selectedDate && selectedDate.date) {
      const selected = parseLocalDate(selectedDate.date);
      if (selected < startDate) {
        setStartDate(selected);
      } else if (selected > addDays(startDate, visibleCount - 1)) {
        setStartDate(selected);
      }
    }
  }, [selectedDate, visibleCount]);

  // Disable left arrow if startDate is today
  const isAtToday = isSameDay(startDate, today);

  const handleArrow = (dir: "left" | "right") => {
    if (dir === "left" && isAtToday) return;
    setStartDate((prev) => addDays(prev, dir === "right" ? 5 : -5));
  };

  return (
    <div className={styles.carouselWrapper}>
      <div className={styles.carouselInner}>
        <button
          aria-label="Scroll left"
          onClick={() => handleArrow("left")}
          disabled={isAtToday}
          className={styles.carouselArrow}
        >
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
            <path d="M13 15L8 10L13 5" stroke="#222" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
        <div
          className={styles.searchCarousel}
          style={{
            display: "flex",
            overflow: "hidden",
            gap: 1,
            width: 5 * 90,
            alignItems: "center",
          }}
        >
          <div className={styles.carouselViewport}>
            <div className={styles.carouselDates}>
              {dates.map((d) => (
                <button
                  key={d.date}
                  onClick={() => onDateSelect(d)}
                  className={
                    selectedDate?.date === d.date
                      ? `${styles.carouselDateBtn} ${styles.carouselDateBtnSelected}`
                      : styles.carouselDateBtn
                  }
                >
                  <div className={styles.dateLabel}>{d.label}</div>
                  <div className={styles.rideCount}>
                    {d.rideCount === 0 ? '-' : `${d.rideCount} ride${d.rideCount === 1 ? '' : 's'}`}
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>
        <button
          aria-label="Scroll right"
          onClick={() => handleArrow("right")}
          className={`${styles.carouselArrow} ${styles.carouselArrowRight}`}
        >
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
            <path d="M7 5L12 10L7 15" stroke="#222" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
      </div>
    </div>
  );
}