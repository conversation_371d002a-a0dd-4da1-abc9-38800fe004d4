import { useState, useEffect, useRef, useCallback } from 'react';
import { Search, ChevronDown } from 'lucide-react';
import styles from './SearchSection.module.css';
import { getLocations } from '../../lib/locations';
import { DateRangeCarousel } from './DateRangeCarousel';
import { getStoredUser } from '../../lib/auth';
import { getSchoolFromEmail } from '../../lib/schoolUtils';

export type SearchFilters = {
  from: string;
  to: string;
  date: string;
  colleges: string[];
};

type SingleDate = {
  date: string;
  label: string;
};

type SearchSectionProps = {
  onSearch: (filters: SearchFilters) => void;
  region: string;
  rides?: any[];
};

function parseLocalDate(dateStr: string) {
  const [year, month, day] = dateStr.split('-').map(Number);
  return new Date(year, month - 1, day);
}

function formatDateLabel(date: Date) {
  return `${date.getDate()} ${date.toLocaleString('default', { month: 'short' })}`;
}

const COLLEGE_OPTIONS = [
  { value: 'cornell', label: 'Cornell' },
  { value: 'ithaca', label: 'Ithaca' },
  { value: 'binghamton', label: 'Binghamton' },
  { value: 'syracuse', label: 'Syracuse' }
];

export function SearchSection({ onSearch, region, rides = [] }: SearchSectionProps) {
  const todayStr = new Date().toISOString().split('T')[0];
  const user = getStoredUser();
  const userSchool = user ? getSchoolFromEmail(user.email) : null;
  const dropdownRef = useRef<HTMLDivElement>(null);

  const [filters, setFilters] = useState<SearchFilters>({
    from: '',
    to: '',
    date: '',
    colleges: userSchool ? [userSchool] : []
  });

  const [selectedDate, setSelectedDate] = useState<SingleDate | null>(null);
  const [dateCounts, setDateCounts] = useState<Record<string, number>>({});
  const [isCollegeDropdownOpen, setIsCollegeDropdownOpen] = useState(false);

  // Initialize with user's school when component mounts
  useEffect(() => {
    if (userSchool) {
      setFilters(prevFilters => {
        const newFilters = { ...prevFilters, colleges: [userSchool] };
        handleSearchWithFilters(newFilters);
        return newFilters;
      });
    }
  }, [userSchool]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsCollegeDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Helper to call onSearch with the correct filters
  const handleSearchWithFilters = useCallback((newFilters: SearchFilters) => {
    setFilters(newFilters);
  }, []);

  // Use useEffect to handle search updates instead of calling during render
  useEffect(() => {
    onSearch(filters);
  }, [filters, onSearch]);

  const handleDateSelect = (date: SingleDate) => {
    if (selectedDate && selectedDate.date === date.date) {
      handleClearDate(); // Unselect if already selected
    } else {
      setSelectedDate(date);
      setFilters((prev) => {
        const newFilters = { ...prev, date: date.date };
        handleSearchWithFilters(newFilters);
        return newFilters;
      });
    }
  };

  const handleClearDate = () => {
    setSelectedDate(null);
    setFilters((prev) => ({ ...prev, date: "" }));
    handleSearchWithFilters({ ...filters, date: "" });
  };

  const handleDateInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setFilters((prev) => ({
      ...prev,
      date: value,
    }));
    if (value) {
      const d = parseLocalDate(value);
      setSelectedDate({ date: value, label: formatDateLabel(d) });
    } else {
      setSelectedDate(null);
    }
  };

  const handleCollegeChange = (collegeValue: string, checked: boolean) => {
    setFilters((prev) => {
      let newColleges: string[];
      if (checked) {
        newColleges = [...prev.colleges, collegeValue];
      } else {
        newColleges = prev.colleges.filter(c => c !== collegeValue);
      }
      const newFilters = { ...prev, colleges: newColleges };
      handleSearchWithFilters(newFilters);
      return newFilters;
    });
  };

  const handleSearch = () => {
    const normalizedFilters = {
      from: filters.from,
      to: filters.to,
      date: filters.date,
      colleges: filters.colleges
    };
    onSearch(normalizedFilters);
  };

  const getTitle = () => {
    return "Ride together, Save together"
  };

  const getSubtitle = () => {
    return region === 'nj'
      ? 'Intercity Carpool Platform for New Jersey'
      : 'Intercity Carpool Platform for Your College Community';
  };

  const getCollegeDropdownText = () => {
    if (filters.colleges.length === 0) return "College";
    if (filters.colleges.length === 1) {
      const college = COLLEGE_OPTIONS.find(c => c.value === filters.colleges[0]);
      return college ? college.label : "College";
    }
    return `${filters.colleges.length} Colleges`;
  };

  const locations = getLocations(region);

  return (
    <div className={styles.container}>
      <h1 className={styles.title}>
        {getTitle()}
      </h1>
      <p className={styles.subtitle}>
        {getSubtitle()}
      </p>

      <div className={styles.searchForm}>
        <div className={styles.formGroup}>
          <select
            className={styles.select}
            value={filters.from}
            onChange={(e) => setFilters({ ...filters, from: e.target.value })}
          >
            <option value="">Departing from</option>
            {locations.map(location => (
              <option key={location} value={location}>{location}</option>
            ))}
          </select>
        </div>

        <div className={styles.formGroup}>
          <select
            className={styles.select}
            value={filters.to}
            onChange={(e) => setFilters({ ...filters, to: e.target.value })}
          >
            <option value="">Going to</option>
            {locations.map(location => (
              <option key={location} value={location}>{location}</option>
            ))}
          </select>
        </div>

        <div className={styles.formGroup}>
          <input
            type="date"
            className={styles.dateInput}
            value={filters.date}
            onChange={handleDateInputChange}
            min={todayStr}
          />
        </div>

        <div className={styles.formGroup}>
          <div className={styles.dropdownContainer} ref={dropdownRef}>
            <button
              type="button"
              className={styles.dropdownButton}
              onClick={() => setIsCollegeDropdownOpen(!isCollegeDropdownOpen)}
            >
              <span>{getCollegeDropdownText()}</span>
              <ChevronDown className={`${styles.dropdownIcon} ${isCollegeDropdownOpen ? styles.rotated : ''}`} />
            </button>
            {isCollegeDropdownOpen && (
              <div className={styles.dropdownContent}>
                {COLLEGE_OPTIONS.map((college) => (
                  <label key={college.value} className={styles.dropdownCheckbox}>
                    <input
                      type="checkbox"
                      checked={filters.colleges.includes(college.value)}
                      onChange={(e) => handleCollegeChange(college.value, e.target.checked)}
                    />
                    <span>{college.label}</span>
                  </label>
                ))}
              </div>
            )}
          </div>
        </div>

        <button
          className={styles.searchButton}
          onClick={handleSearch}
        >
          <Search className={styles.searchIcon} />
          <span>Search</span>
        </button>
      </div>
      {/* Render DateRangeCarousel below the search bar */}
      <div style={{ display: "flex", justifyContent: "center" }}>
        <DateRangeCarousel
          selectedDate={selectedDate}
          onDateSelect={handleDateSelect}
          visibleCount={5}
          rides={rides}
        />
      </div>
    </div>
  );
}
