import React, { useState, useEffect } from 'react';
import { Modal } from '../Modal';
import { AlertCircle } from 'lucide-react';
import { mockGoogleSignIn, storeUser } from '../../lib/auth';
import { Loader } from '../Loader';
import styles from './AuthModal.module.css';
import { auth } from '../../lib/firebase'
import type { User } from '../../types';

type AuthModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onSignIn: (user?: User) => void;
  onSwitchToRegister: () => void;
};

export function AuthModal({ isOpen, onClose, onSignIn, onSwitchToRegister }: AuthModalProps) {
  const [error, setError] = useState('');
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);
  const [acceptedTerms, setAcceptedTerms] = useState(false);

  useEffect(() => {
    const agreed = localStorage.getItem('acceptedTerms') === 'true';
    setAcceptedTerms(agreed);
  })

  const handleTermsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const isChecked = e.target.checked;
    setAcceptedTerms(isChecked);
    localStorage.setItem('acceptedTerms', isChecked ? 'true' : 'false')
  }

  const handleGoogleSignIn = async () => {
    setIsGoogleLoading(true);
    setError('');

    if (!acceptedTerms) {
      setError("You must accept the Terms and Conditions")
      setIsGoogleLoading(false)
      return
    }

    try {
      const user = await mockGoogleSignIn();

      const isDev = process.env.NODE_ENV !== 'production';

      // if (user // &&
      //   // !isDev &&
      //   // !user.email.toLowerCase().endsWith('@cornell.edu') &&
      //   // !user.email.toLowerCase().endsWith('@bloodandtreasure.com')
      // ) {
      //   setError('Only Cornell email addresses are allowed.');
      //   auth.signOut().catch(e => console.error("Error signing out:", e));
      //   return;
      // }

      if (user) {
        onSignIn(user);
        onClose();
      } else {
        setError('Google sign-in failed. Please try again.');
      }
    } catch (error: any) {
      console.error('Google sign-in error:', error);

      // Display the specific error message
      setError(error.message || 'Unable to sign in with Google. Please try again later.');

      // Force Google to prompt for account selection on next attempt
      auth.signOut().catch(e => console.error("Error signing out:", e));
    } finally {
      setIsGoogleLoading(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <div className={styles.container}>
        <h3 className={styles.title}>
          Sign in to Kamel Ride
        </h3>

        {error && (
          <div className={styles.errorContainer}>
            <AlertCircle className="w-4 h-4" />
            {error}
          </div>
        )}

        <div className={styles.divider}>
          <div className={styles.dividerLine}>
            <div className={styles.dividerBorder}></div>
          </div>
          <div className={styles.dividerText}>
            <span className={styles.dividerTextContent}>Please sign in with your university email address</span>
          </div>
        </div>

        <button
          onClick={handleGoogleSignIn}
          disabled={isGoogleLoading}
          className={styles.googleButton}
        >
          {isGoogleLoading ? (
            <>
              <Loader size="sm" className="mr-2" />
              <span>Connecting to Google...</span>
            </>
          ) : (
            <>
              <svg className="w-5 h-5" viewBox="0 0 24 24">
                <path
                  fill="currentColor"
                  d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                />
                <path
                  fill="currentColor"
                  d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                />
                <path
                  fill="currentColor"
                  d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                />
                <path
                  fill="currentColor"
                  d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                />
              </svg>
              <span>Continue with Google</span>
            </>
          )}
        </button>
        <label className={styles.termsLabel}>
          <input type="checkbox" className={styles.checkbox} checked={acceptedTerms} onChange={handleTermsChange} />
          <a href="https://info.kamelride.com/terms-of-service" target="_blank" rel="noopener noreferrer" className={styles.termsLink}>
            Accept Terms and Conditions
          </a>
        </label>
        {/* <div className={styles.signupContainer}>
          <span className={styles.signupText}>Don't have an account?</span>{' '}
          <button
            type="button"
            onClick={onSwitchToRegister}
            className={styles.signupButton}
          >
            Sign up
          </button>
        </div> */}
      </div>
    </Modal>
  );
}
