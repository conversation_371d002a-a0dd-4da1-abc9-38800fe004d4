import { DataTypes, Optional } from 'sequelize';
import { BaseModel, addSchemaHook } from './baseModel.js';
import { sequelize } from '../config/database.js';
import { User } from './userModel.js';

// RideRequest attributes interface
interface RideRequestAttributes {
  id: string;
  from: string;
  to: string;
  date: string;
  time: string;
  seatsNeeded: number;
  suitcasesNeeded: number;
  notes: string[] | null;
  userId: string;
  createdAt?: Date;
  updatedAt?: Date;
  preferredPickupAddress?: string | null;
  preferredPickupLat?: number | null;
  preferredPickupLng?: number | null;
  preferredDropoffAddress?: string | null;
  preferredDropoffLat?: number | null;
  preferredDropoffLng?: number | null;
}

// Interface for RideRequest creation attributes
interface RideRequestCreationAttributes extends Optional<RideRequestAttributes, 'id' | 'notes'> {}

// RideRequest model class extending BaseModel
export class RideRequest extends BaseModel<RideRequestAttributes, RideRequestCreationAttributes> implements RideRequestAttributes {
  public id!: string;
  public from!: string;
  public to!: string;
  public date!: string;
  public time!: string;
  public seatsNeeded!: number;
  public suitcasesNeeded!: number;
  public notes!: string[] | null;
  public userId!: string;

  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  public preferredPickupAddress?: string | null;
  public preferredPickupLat?: number | null;
  public preferredPickupLng?: number | null;
  public preferredDropoffAddress?: string | null;
  public preferredDropoffLat?: number | null;
  public preferredDropoffLng?: number | null;

  // Virtual field for posted time
  public get postedTime(): string {
    const now = new Date();
    const created = new Date(this.createdAt);
    const diffTime = Math.abs(now.getTime() - created.getTime());
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays > 0) {
      return `${diffDays} ${diffDays === 1 ? 'day' : 'days'} ago`;
    }
    
    const diffHours = Math.floor(diffTime / (1000 * 60 * 60));
    if (diffHours > 0) {
      return `${diffHours} ${diffHours === 1 ? 'hour' : 'hours'} ago`;
    }
    
    const diffMinutes = Math.floor(diffTime / (1000 * 60));
    return `${diffMinutes} ${diffMinutes === 1 ? 'minute' : 'minutes'} ago`;
  }
}

// Initialize RideRequest model
RideRequest.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    from: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    to: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    date: {
      type: DataTypes.DATEONLY,
      allowNull: false,
    },
    time: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    seatsNeeded: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    suitcasesNeeded: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    notes: {
      type: DataTypes.ARRAY(DataTypes.STRING),
      allowNull: true,
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    preferredPickupAddress: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    preferredPickupLat: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    preferredPickupLng: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    preferredDropoffAddress: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    preferredDropoffLat: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    preferredDropoffLng: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
  },
  {
    sequelize: BaseModel.getSequelizeForRequest(),
    modelName: 'RideRequest',
    tableName: 'riderequests',
  }
);

// Add schema hook instead of connection hooks
addSchemaHook(RideRequest);

// Define associations
RideRequest.belongsTo(User, { 
  foreignKey: 'userId', 
  as: 'passenger',
  constraints: false
});

User.hasMany(RideRequest, { 
  foreignKey: 'userId', 
  as: 'riderequests',
  constraints: false
});

export default RideRequest;
