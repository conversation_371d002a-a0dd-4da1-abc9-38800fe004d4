import { DataTypes, Optional } from 'sequelize';
import { BaseModel, addSchemaHook } from './baseModel.js';
import { sequelize } from '../config/database.js';
import { User } from './userModel.js';

// Ride attributes interface
interface RideAttributes {
  id: string;
  from: string;
  to: string;
  date: string;
  time: string;
  seatsAvailable: number;
  totalSeats: number;
  suitcasesAvailable: number;
  totalSuitcases: number;
  price: number;
  notes: string[] | null;
  driverId: string;
  createdAt?: Date;
  updatedAt?: Date;
  pickupAddress?: string | null;
  pickupLat?: number | null;
  pickupLng?: number | null;
  dropoffAddress?: string | null;
  dropoffLat?: number | null;
  dropoffLng?: number | null;
}

// Interface for Ride creation attributes
interface RideCreationAttributes extends Optional<RideAttributes, 'id' | 'notes'> {}

// Ride model class extending BaseModel
export class Ride extends BaseModel<RideAttributes, RideCreationAttributes> implements RideAttributes {
  public id!: string;
  public from!: string;
  public to!: string;
  public date!: string;
  public time!: string;
  public seatsAvailable!: number;
  public totalSeats!: number;
  public suitcasesAvailable!: number;
  public totalSuitcases!: number;
  public price!: number;
  public notes!: string[] | null;
  public driverId!: string;

  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  public pickupAddress?: string | null;
  public pickupLat?: number | null;
  public pickupLng?: number | null;
  public dropoffAddress?: string | null;
  public dropoffLat?: number | null;
  public dropoffLng?: number | null;

  // Virtual field for posted time
  public get postedTime(): string {
    const now = new Date();
    const created = new Date(this.createdAt);
    const diffTime = Math.abs(now.getTime() - created.getTime());
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays > 0) {
      return `${diffDays} ${diffDays === 1 ? 'day' : 'days'} ago`;
    }
    
    const diffHours = Math.floor(diffTime / (1000 * 60 * 60));
    if (diffHours > 0) {
      return `${diffHours} ${diffHours === 1 ? 'hour' : 'hours'} ago`;
    }
    
    const diffMinutes = Math.floor(diffTime / (1000 * 60));
    return `${diffMinutes} ${diffMinutes === 1 ? 'minute' : 'minutes'} ago`;
  }
}

// Initialize Ride model
Ride.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    from: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    to: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    date: {
      type: DataTypes.DATEONLY,
      allowNull: false,
    },
    time: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    seatsAvailable: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    totalSeats: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    suitcasesAvailable: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    totalSuitcases: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
    },
    notes: {
      type: DataTypes.ARRAY(DataTypes.STRING),
      allowNull: true,
    },
    driverId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    pickupAddress: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    pickupLat: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    pickupLng: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    dropoffAddress: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    dropoffLat: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    dropoffLng: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
  },
  {
    sequelize: BaseModel.getSequelizeForRequest(),
    modelName: 'Ride',
    tableName: 'rides',
    // Remove explicit schema
  }
);

// Add schema hook
addSchemaHook(Ride);

// Define associations without the incorrect scope
Ride.belongsTo(User, { 
  foreignKey: 'driverId', 
  as: 'driver',
  constraints: false
});

User.hasMany(Ride, { 
  foreignKey: 'driverId', 
  as: 'rides',
  constraints: false
});

export default Ride;
