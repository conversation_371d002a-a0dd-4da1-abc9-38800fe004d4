import express from "express";
import cors from "cors";
import morgan from "morgan";
import dotenv from "dotenv";
import { testConnection } from "./config/database.js";
import { initializeDatabase } from "./config/initDb.js";
import { errorHandler } from "./middleware/errorMiddleware.js";
import { createServer } from "http";
import { initializeSocket } from "./config/socket.js";
import { databaseMiddleware } from "./middleware/databaseMiddleware.js";
import { startCronJobs } from "./utils/cronJobs.js";

// Routes
import userRoutes from "./routes/userRoutes.js";
import authRoutes from "./routes/authRoutes.js";
import rideRoutes from "./routes/rideRoutes.js";
import bookingRoutes from "./routes/bookingRoutes.js";
import messageRoutes from "./routes/messageRoutes.js";
import rideRequestRoutes from "./routes/rideRequestRoutes.js";
import subscriptionRoutes from './routes/subscriptionRoutes.js';
import paymentRoutes from './routes/paymentRoutes.js';
import conversationMessageRoutes from './routes/conversationMessageRoutes.js'
import reviewRoutes from './routes/reviewRoutes.js'
import stripeRoutes from './routes/stripeRoutes.js';
import payoutRoutes from './routes/payoutRoutes.js';

dotenv.config();

const app = express();
const PORT = process.env.PORT || 5000;

console.log("DB Host:", process.env.DB_HOST);
console.log("DB Name:", process.env.DB_NAME);
console.log("DB User:", process.env.DB_USER);

app.use(
  cors({
    origin: "*",
    methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
    allowedHeaders: [
      "Content-Type",
      "Authorization",
      "X-Requested-With",
      "Accept",
      "Origin",
      "X-Region",
      "x-region",
    ],
    credentials: true,
  })
);

app.use(express.json());
app.use(morgan("dev"));

// Apply database middleware to all requests
app.use(databaseMiddleware);

const httpServer = createServer(app);
export const io = initializeSocket(httpServer);

// API Routes
app.use("/api/users", userRoutes);
app.use("/api/auth", authRoutes);
app.use("/api/rides", rideRoutes);
app.use("/api/bookings", bookingRoutes);
app.use("/api/messages", messageRoutes);
app.use("/api/ride-requests", rideRequestRoutes);
app.use("/api/subscriptions", subscriptionRoutes);
app.use('/api/payments', paymentRoutes);
app.use("/api/conversation-messages", conversationMessageRoutes);
app.use("/api/reviews", reviewRoutes)
app.use('/api/stripe', stripeRoutes);
app.use('/api/payouts', payoutRoutes);

// Health check route
app.get("/health", (req, res) => {
  res.status(200).json({ status: "ok" });
});

// Error handling middleware
app.use(errorHandler);

// Database connection and server start
const startServer = async () => {
  try {
    // Test database connection for default region
    const connected = await testConnection();

    if (!connected) {
      console.error("Failed to connect to the database. Exiting...");
      process.exit(1);
    }

    // Initialize database and create tables
    await initializeDatabase(false);

    // Beginning cron jobs after database is ready
    startCronJobs();

    // Start server
    httpServer.listen(PORT, () => {
      console.log(
        `Server running on port ${PORT} in ${process.env.NODE_ENV} mode`
      );
    });
  } catch (error) {
    console.error("Error starting server:", error);
    process.exit(1);
  }
};

startServer();

export default app;
