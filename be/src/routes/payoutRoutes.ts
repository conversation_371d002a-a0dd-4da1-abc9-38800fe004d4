import express from 'express';
import { triggerPayouts } from '../controllers/payoutController.js';
import { protect, authorize } from '../middleware/authMiddleware.js';

const router = express.Router();

// Manual trigger for testing - admin only in production
if (process.env.NODE_ENV === 'production') {
  // Production
  router.post('/trigger', protect, authorize('admin'), triggerPayouts);
} else {
  // Development
  router.post('/trigger', protect, triggerPayouts);
}

export default router;