import cron from 'node-cron';
import { autoRejectExpiredBookings, sendDriverReminders } from './bookingExpiry.js';
import { processDriverPayouts } from '../controllers/payoutController.js';
import { Ride } from '../models/rideModel.js';
import { Booking, BookingStatus } from '../models/bookingModel.js';
import User from '../models/userModel.js';

const TESTING_MODE = process.env.BOOKING_TESTING_MODE === 'true';

export const startCronJobs = () => {
  // Only run cron jobs on the PM2 instance 0 to prevent duplicate reminder emails
  const instanceId = process.env.NODE_APP_INSTANCE || process.env.PM2_INSTANCE_ID || '0';
  
  console.log(`Detected instance ID: ${instanceId}`);
  
  if (instanceId !== '0') {
    console.log(`Instance ${instanceId} - Skipping cron jobs (only instance 0 runs cron jobs)`);
    return;
  }

  console.log(`Instance ${instanceId} - WILL RUN CRON JOBS`);

  // Use different schedules for testing vs production
  const schedule = TESTING_MODE ? '*/10 * * * * *' : '0 * * * *'; // Every 10 seconds vs every hour
  
  cron.schedule(schedule, async () => {
    console.log(`Instance ${instanceId} - ${TESTING_MODE ? 'TEST MODE' : 'PRODUCTION'} - Running booking management cron jobs...`);
    
    try {
      await sendDriverReminders();
      await autoRejectExpiredBookings();
      await markRidesAsCompleted();
    } catch (error) {
      console.error('Booking management cron jobs failed:', error);
    }
  });

  // Driver payout cron job - runs every hour (or every 10 seconds in testing)
  cron.schedule(schedule, async () => {
    console.log(`Instance ${instanceId} - ${TESTING_MODE ? 'TEST MODE' : 'PRODUCTION'} - Running driver payout check...`);
    
    try {
      await processDriverPayouts({ driverEmail: '<EMAIL>' }); // Pass filter for testing
    } catch (error) {
      console.error('Driver payout cron job failed:', error);
    }
  });

  console.log(`Instance ${instanceId} - Cron jobs started successfully in ${TESTING_MODE ? 'TESTING' : 'PRODUCTION'} mode`);
};

export const markRidesAsCompleted = async () => {
  try {
    const now = new Date();

    // 1 minute in test, 12 hours in production
    const hoursToWait = TESTING_MODE ? (1/60) : 12;
    const timeAgo = new Date();
    timeAgo.setHours(timeAgo.getHours() - hoursToWait);

    console.log('--- Booking Completion Debug ---');
    console.log('Current UTC time:', now.toISOString());
    console.log('Cutoff time:', timeAgo.toISOString());
    console.log(`${TESTING_MODE ? 'TEST MODE' : 'PRODUCTION'} - Checking for rides to mark as completed (rides older than ${TESTING_MODE ? '1 minute' : '12 hours'})`);

    // Only process rides <NAME_EMAIL> for testing
    const ridesQuery = await Ride.findAll({
      include: [
        {
          model: Booking,
          as: 'bookings',
          where: {
            status: BookingStatus.CONFIRMED
          }
        },
        {
          model: User,
          as: 'driver',
          where: {
            email: '<EMAIL>'
          }
        }
      ]
    }) as (Ride & { bookings: Booking[] })[];

    console.log(`Found ${ridesQuery.length} rides with confirmed bookings`);
    ridesQuery.forEach(ride => {
      const rideDateTime = new Date(`${ride.date}T${ride.time}:00Z`);
      console.log(
        `Ride ${ride.id} scheduled for: ${ride.date} ${ride.time} (UTC: ${rideDateTime.toISOString()})`
      );
      if (ride.bookings && ride.bookings.length > 0) {
        ride.bookings.forEach(b => {
          console.log(`  Booking ${b.id}: status=${b.status}`);
        });
      } else {
        console.log('  No confirmed bookings for this ride.');
      }
    });
    ridesQuery.forEach(ride => {
      const rideDateTime = new Date(`${ride.date}T${ride.time}`);
      console.log(
        `Ride ${ride.id}: ${ride.date} ${ride.time} (UTC: ${rideDateTime.toISOString()})`
      );
      if (ride.bookings && ride.bookings.length > 0) {
        ride.bookings.forEach(b => {
          console.log(`  Booking ${b.id}: status=${b.status}`);
        });
      } else {
        console.log('  No confirmed bookings for this ride.');
      }
    });

    // Filter rides where date + time has passed AND enough time has elapsed
    const completedRides = ridesQuery.filter(ride => {
      // Force UTC parsing
      const rideDateTime = new Date(`${ride.date}T${ride.time}:00Z`);
      console.log(
        `Checking ride ${ride.id}: rideDateTime=${rideDateTime.toISOString()}, timeAgo=${timeAgo.toISOString()}, eligible=${rideDateTime <= timeAgo}`
      );
      return rideDateTime <= timeAgo;
    });

    if (completedRides.length === 0) {
      console.log('No rides eligible for completion at this time.');
    }

    let completedCount = 0;
    for (const ride of completedRides) {
      const rideDateTime = new Date(`${ride.date}T${ride.time}`);
      console.log(
        `Attempting to update bookings for rideId=${ride.id} where status='${BookingStatus.CONFIRMED}'`
      );
      const updated = await Booking.update(
        { 
          status: BookingStatus.COMPLETED,
          updatedAt: rideDateTime
        },
        {
          where: {
            rideId: ride.id,
            status: BookingStatus.CONFIRMED
          }
        }
      );
      console.log(`Booking.update affected rows: ${updated[0]}`);
      if (updated[0] > 0) {
        completedCount++;
        console.log(`✅ Marked ${updated[0]} bookings as completed for ride ${ride.id} (${ride.from} → ${ride.to} at ${ride.date} ${ride.time})`);
      }
    }

    console.log(`Marked ${completedCount} rides as completed`);
    console.log('--- End Booking Completion Debug ---');
  } catch (error) {
    console.error('Error marking rides as completed:', error);
  }
};