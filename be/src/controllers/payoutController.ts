import Stripe from 'stripe';
import { Booking, BookingStatus } from '../models/bookingModel.js';
import { Ride } from '../models/rideModel.js';
import { User } from '../models/userModel.js';
import { Op } from 'sequelize';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, { apiVersion: "2025-06-30.basil" });
const STRIPE_ENABLED = process.env.STRIPE_ENABLED === 'true';
const TESTING_MODE = process.env.BOOKING_TESTING_MODE === 'true';

// Define type for booking with included associations
interface BookingWithAssociations extends Booking {
  ride: Ride & {
    driver: User;
  };
  user: User;
}

export const processDriverPayouts = async (opts?: { driverEmail?: string }) => {
  if (!STRIPE_ENABLED) {
    console.log('Stripe is disabled, skipping payouts');
    return;
  }

  try {
    const hoursToWait = TESTING_MODE ? (1/60) : 12; // 1 minute in test, 12 hours in production

    const timeAgo = new Date();
    timeAgo.setHours(timeAgo.getHours() - hoursToWait);

    const driverEmail = opts?.driverEmail || '<EMAIL>'; // <--- force filter for testing

    console.log(`${TESTING_MODE ? 'TEST MODE' : 'PRODUCTION'} - Checking for bookings completed before: ${timeAgo.toISOString()} for driver ${driverEmail}`);

    // Only find bookings for the specified driver
    const bookingsNeedingPayout = await Booking.findAll({
      where: {
        status: BookingStatus.COMPLETED,
        driverPaymentProcessed: false,
        updatedAt: {
          [Op.lt]: timeAgo
        }
      },
      include: [
        {
          model: Ride,
          as: 'ride',
          include: [
            {
              model: User,
              as: 'driver',
              attributes: ['id', 'name', 'email', 'stripeAccountId'],
              where: { email: driverEmail }
            }
          ]
        },
        {
          model: User,
          as: 'user',
          attributes: ['id', 'name', 'email']
        }
      ]
    }) as BookingWithAssociations[];

    // Log all bookings and their ride/driver info for debugging
    bookingsNeedingPayout.forEach(b => {
      console.log(
        `Booking ${b.id}: rideId=${b.rideId}, ` +
        `ride=${b.ride ? b.ride.id : 'null'}, ` +
        `driver=${b.ride && b.ride.driver ? b.ride.driver.id : 'null'}, ` +
        `driverEmail=${b.ride && b.ride.driver ? b.ride.driver.email : 'null'}`
      );
    });

    // Count and log bookings with missing ride or driver
    const invalidBookings = bookingsNeedingPayout.filter(
      b => !b.ride || !b.ride.driver
    );
    const validBookings = bookingsNeedingPayout.filter(
      b => b.ride && b.ride.driver
    );

    console.log(
      `Found ${validBookings.length} valid bookings and ${invalidBookings.length} bookings with null ride or driver for driver ${driverEmail}`
    );

    if (invalidBookings.length > 0) {
      console.log('Bookings with null ride or driver:', invalidBookings.map(b => b.id));
    }

    // Group bookings by ride for processing
    const bookingsByRide = validBookings.reduce((acc, booking) => {
      const rideId = booking.rideId;
      if (!acc[rideId]) {
        acc[rideId] = {
          ride: booking.ride,
          bookings: []
        };
      }
      acc[rideId].bookings.push(booking);
      return acc;
    }, {} as Record<string, { ride: Ride & { driver: User }, bookings: BookingWithAssociations[] }>);

    for (const rideData of Object.values(bookingsByRide)) {
      await processRidePayouts(rideData);
    }

  } catch (error) {
    console.error('Error processing driver payouts:', error);
  }
};

const processRidePayouts = async (rideData: { ride: Ride & { driver: User }, bookings: BookingWithAssociations[] }) => {
  const { ride, bookings } = rideData;
  const driver = ride.driver;

  if (!driver.stripeAccountId) {
    console.log(`⚠️ Driver ${driver.name} (${driver.id}) doesn't have a Stripe account, skipping ${bookings.length} completed bookings for ride ${ride.id} (${ride.from} → ${ride.to})`);
    return;
  }

  console.log(`💰 Processing payouts for ${bookings.length} completed bookings on ride ${ride.id} (${ride.from} → ${ride.to}) for driver ${driver.name}`);

  let successfulPayouts = 0;
  let failedPayouts = 0;

  for (const booking of bookings) {
    try {
      // Calculate amounts
      const totalAmount = booking.totalAmount || (ride.price * booking.seats * 100);
      const platformFeeAmount = Math.round(totalAmount * (booking.platformFeePercentage / 100));
      const driverAmount = totalAmount - platformFeeAmount;

      console.log(`  📊 Booking ${booking.id}: Total: $${totalAmount/100}, Platform Fee: $${platformFeeAmount/100}, Driver Payout: $${driverAmount/100}`);

      // Create Stripe transfer (works with both test and live API keys)
      const transfer = await stripe.transfers.create({
        amount: driverAmount,
        currency: 'usd',
        destination: driver.stripeAccountId,
        metadata: {
          bookingId: booking.id,
          rideId: ride.id,
          driverId: driver.id,
          completedAt: booking.updatedAt.toISOString(),
          totalAmount: totalAmount.toString(),
          platformFee: platformFeeAmount.toString(),
          testMode: TESTING_MODE.toString()
        }
      });

      // Update booking as processed
      await Booking.update({
        driverPaymentProcessed: true,
        driverPaymentDate: new Date(),
        totalAmount: totalAmount
      }, {
        where: { id: booking.id }
      });

      console.log(`  ✅ ${TESTING_MODE ? 'TEST' : 'LIVE'}: Payout processed for booking ${booking.id} - $${driverAmount/100} to driver ${driver.name} (Transfer ID: ${transfer.id})`);
      successfulPayouts++;

    } catch (error) {
      console.error(`  ❌ Failed to process payout for booking ${booking.id}:`, error);
      failedPayouts++;
    }
  }

  console.log(`📈 Ride ${ride.id} payout summary: ${successfulPayouts} successful, ${failedPayouts} failed`);
};

// Manual trigger endpoint for testing
export const triggerPayouts = async (req: any, res: any) => {
  try {
    console.log(`Manual payout trigger in ${TESTING_MODE ? 'TEST' : 'PRODUCTION'} mode`);
    await processDriverPayouts();
    res.json({ 
      message: 'Payouts processed successfully',
      testMode: TESTING_MODE 
    });
  } catch (error) {
    console.error('Error triggering payouts:', error);
    res.status(500).json({ error: 'Failed to process payouts' });
  }
};