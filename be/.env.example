NJ_DB_NAME=

DB_HOST=
DB_USER=
DB_NAME=
DB_PASSWORD=
DB_PORT=

PORT=3001
NODE_ENV= development

CLIENT_URL=https://app.kamelride.com

FIREBASE_PROJECT_ID=kamel-ride-beta
FIREBASE_CLIENT_EMAIL=
FIREBASE_PRIVATE_KEY=

# KLAVIYO_API_KEY=
KLAVIYO_API_KEY=
KLAYVIYO_TEMPLATE_ID=

AWS_S3_BUCKET_URL=
AWS_S3_BUCKET=
AWS_ACCESS_KEY_ID=
AWS_SECRET_KEY=

SENDGRID_API_KEY=
SENDGRID_PASSENGER_CANCEL_TEMPLATE_ID=
SENDGRID_DRIVER_CANCEL_TEMPLATE_ID=
SENDGRID_NEW_PASSENGER_BOOKING_TEMPLATE_ID=
SENDGRID_PASSENGER_ACCEPTED_TEMPLATE_ID=
SENDGRID_PASSENGER_REJECTED_TEMPLATE_ID=
SENDGRID_NEW_MESSAGE_TEMPLATE_ID=
SENDGRID_PRIVATE_MESSAGE_TEMPLATE_ID=
SENDGRID_DRIVER_REMINDER_TEMPLATE_ID=
SENDGRID_PASSENGER_AUTO_REJECTED_TEMPLATE_ID=

STRIPE_SECRET_KEY=
STRIPE_REFRESH_URL=
STRIPE_RETURN_URL=
STRIPE_ENABLED=

BOOKING_TESTING_MODE=