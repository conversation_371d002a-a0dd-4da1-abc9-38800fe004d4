module.exports = {
  up: async (queryInterface, Sequelize) => {
    const tableInfo = await queryInterface.describeTable('bookings');
    
    if (!tableInfo.reminderSent) {
      await queryInterface.addColumn('bookings', 'reminderSent', {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: 'Flag to track if 23-hour reminder email has been sent to driver',
      });
    } else {
      console.log('Column reminderSent already exists, skipping...');
    }
  },

  down: async (queryInterface, Sequelize) => {
    const tableInfo = await queryInterface.describeTable('bookings');
    
    if (tableInfo.reminderSent) {
      await queryInterface.removeColumn('bookings', 'reminderSent');
    }
  }
};