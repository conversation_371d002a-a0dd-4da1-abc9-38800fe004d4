module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Remove the existing foreign key constraint
    await queryInterface.removeConstraint('bookings', 'bookings_rideId_fkey');
    // Add a new foreign key constraint with ON DELETE CASCADE
    await queryInterface.addConstraint('bookings', {
      fields: ['rideId'],
      type: 'foreign key',
      name: 'bookings_rideId_fkey',
      references: {
        table: 'rides',
        field: 'id',
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
  },

  down: async (queryInterface, Sequelize) => {
    // Remove the CASCADE constraint
    await queryInterface.removeConstraint('bookings', 'bookings_rideId_fkey');
    // Add the original constraint (no cascade)
    await queryInterface.addConstraint('bookings', {
      fields: ['rideId'],
      type: 'foreign key',
      name: 'bookings_rideId_fkey',
      references: {
        table: 'rides',
        field: 'id',
      },
      onDelete: 'NO ACTION',
      onUpdate: 'CASCADE',
    });
  }
};