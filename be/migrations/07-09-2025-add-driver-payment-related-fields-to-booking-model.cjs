module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Check if columns already exist
    const tableDescription = await queryInterface.describeTable('bookings');
    
    if (!tableDescription.totalAmount) {
      // Add totalAmount as nullable first
      await queryInterface.addColumn('bookings', 'totalAmount', {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: 'Total amount paid by passenger in cents (price * seats)'
      });

      // Calculate totalAmount for existing bookings
      await queryInterface.sequelize.query(`
        UPDATE bookings 
        SET "totalAmount" = (
          SELECT (rides.price * bookings.seats * 100)
          FROM rides 
          WHERE rides.id = bookings."rideId"
        )
        WHERE "totalAmount" IS NULL
      `);

      // Now make it NOT NULL since all records have values
      await queryInterface.changeColumn('bookings', 'totalAmount', {
        type: Sequelize.INTEGER,
        allowNull: false,
        comment: 'Total amount paid by passenger in cents (price * seats)'
      });
    }

    if (!tableDescription.driverPaymentProcessed) {
      await queryInterface.addColumn('bookings', 'driverPaymentProcessed', {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false
      });
    }

    if (!tableDescription.driverPaymentDate) {
      await queryInterface.addColumn('bookings', 'driverPaymentDate', {
        type: Sequelize.DATE,
        allowNull: true
      });
    }

    if (!tableDescription.platformFeePercentage) {
      await queryInterface.addColumn('bookings', 'platformFeePercentage', {
        type: Sequelize.DECIMAL(5, 2),
        allowNull: false,
        defaultValue: 0.00
      });
    }

    // Create index with correct column names
    try {
      await queryInterface.addIndex('bookings', 
        ['status', 'driverPaymentProcessed', 'updatedAt'], 
        {
          name: 'idx_bookings_payout_lookup'
        }
      );
    } catch (error) {
      // Index might already exist, ignore the error
      if (!error.message.includes('already exists')) {
        throw error;
      }
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeIndex('bookings', 'idx_bookings_payout_lookup');
    } catch (error) {
      // Index might not exist, ignore
    }
    
    const tableDescription = await queryInterface.describeTable('bookings');
    
    if (tableDescription.platformFeePercentage) {
      await queryInterface.removeColumn('bookings', 'platformFeePercentage');
    }
    if (tableDescription.driverPaymentDate) {
      await queryInterface.removeColumn('bookings', 'driverPaymentDate');
    }
    if (tableDescription.driverPaymentProcessed) {
      await queryInterface.removeColumn('bookings', 'driverPaymentProcessed');
    }
    if (tableDescription.totalAmount) {
      await queryInterface.removeColumn('bookings', 'totalAmount');
    }
  }
};