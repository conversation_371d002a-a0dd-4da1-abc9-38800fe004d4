module.exports = {
  up: async (queryInterface, Sequelize) => {
    const tableInfo = await queryInterface.describeTable('riderequests');

    if (!tableInfo.preferredPickupAddress) {
      await queryInterface.addColumn('riderequests', 'preferredPickupAddress', {
        type: Sequelize.STRING,
        allowNull: true,
        comment: 'Preferred pick up address entered by user',
      });
    }
    if (!tableInfo.preferredPickupLat) {
      await queryInterface.addColumn('riderequests', 'preferredPickupLat', {
        type: Sequelize.FLOAT,
        allowNull: true,
        comment: 'Latitude for preferred pick up location',
      });
    }
    if (!tableInfo.preferredPickupLng) {
      await queryInterface.addColumn('riderequests', 'preferredPickupLng', {
        type: Sequelize.FLOAT,
        allowNull: true,
        comment: 'Longitude for preferred pick up location',
      });
    }
    if (!tableInfo.preferredDropoffAddress) {
      await queryInterface.addColumn('riderequests', 'preferredDropoffAddress', {
        type: Sequelize.STRING,
        allowNull: true,
        comment: 'Preferred drop off address entered by user',
      });
    }
    if (!tableInfo.preferredDropoffLat) {
      await queryInterface.addColumn('riderequests', 'preferredDropoffLat', {
        type: Sequelize.FLOAT,
        allowNull: true,
        comment: 'Latitude for preferred drop off location',
      });
    }
    if (!tableInfo.preferredDropoffLng) {
      await queryInterface.addColumn('riderequests', 'preferredDropoffLng', {
        type: Sequelize.FLOAT,
        allowNull: true,
        comment: 'Longitude for preferred drop off location',
      });
    }
  },

  down: async (queryInterface, Sequelize) => {
    const tableInfo = await queryInterface.describeTable('riderequests');

    if (tableInfo.preferredPickupAddress) {
      await queryInterface.removeColumn('riderequests', 'preferredPickupAddress');
    }
    if (tableInfo.preferredPickupLat) {
      await queryInterface.removeColumn('riderequests', 'preferredPickupLat');
    }
    if (tableInfo.preferredPickupLng) {
      await queryInterface.removeColumn('riderequests', 'preferredPickupLng');
    }
    if (tableInfo.preferredDropoffAddress) {
      await queryInterface.removeColumn('riderequests', 'preferredDropoffAddress');
    }
    if (tableInfo.preferredDropoffLat) {
      await queryInterface.removeColumn('riderequests', 'preferredDropoffLat');
    }
    if (tableInfo.preferredDropoffLng) {
      await queryInterface.removeColumn('riderequests', 'preferredDropoffLng');
    }
  }
};