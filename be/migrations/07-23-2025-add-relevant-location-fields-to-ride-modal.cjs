module.exports = {
  up: async (queryInterface, Sequelize) => {
    const tableInfo = await queryInterface.describeTable('rides');

    if (!tableInfo.pickupAddress) {
      await queryInterface.addColumn('rides', 'pickupAddress', {
        type: Sequelize.STRING,
        allowNull: true,
        comment: 'Preferred pick up address for the ride',
      });
    }
    if (!tableInfo.pickupLat) {
      await queryInterface.addColumn('rides', 'pickupLat', {
        type: Sequelize.FLOAT,
        allowNull: true,
        comment: 'Latitude for pick up location',
      });
    }
    if (!tableInfo.pickupLng) {
      await queryInterface.addColumn('rides', 'pickupLng', {
        type: Sequelize.FLOAT,
        allowNull: true,
        comment: 'Longitude for pick up location',
      });
    }
    if (!tableInfo.dropoffAddress) {
      await queryInterface.addColumn('rides', 'dropoffAddress', {
        type: Sequelize.STRING,
        allowNull: true,
        comment: 'Preferred drop off address for the ride',
      });
    }
    if (!tableInfo.dropoffLat) {
      await queryInterface.addColumn('rides', 'dropoffLat', {
        type: Sequelize.FLOAT,
        allowNull: true,
        comment: 'Latitude for drop off location',
      });
    }
    if (!tableInfo.dropoffLng) {
      await queryInterface.addColumn('rides', 'dropoffLng', {
        type: Sequelize.FLOAT,
        allowNull: true,
        comment: 'Longitude for drop off location',
      });
    }
  },

  down: async (queryInterface, Sequelize) => {
    const tableInfo = await queryInterface.describeTable('rides');

    if (tableInfo.pickupAddress) {
      await queryInterface.removeColumn('rides', 'pickupAddress');
    }
    if (tableInfo.pickupLat) {
      await queryInterface.removeColumn('rides', 'pickupLat');
    }
    if (tableInfo.pickupLng) {
      await queryInterface.removeColumn('rides', 'pickupLng');
    }
    if (tableInfo.dropoffAddress) {
      await queryInterface.removeColumn('rides', 'dropoffAddress');
    }
    if (tableInfo.dropoffLat) {
      await queryInterface.removeColumn('rides', 'dropoffLat');
    }
    if (tableInfo.dropoffLng) {
      await queryInterface.removeColumn('rides', 'dropoffLng');
    }
  }
};